import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';
import usePageTitle from '../../hooks/usePageTitle';
import {
  Container,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveTable,
  ResponsiveButton,
  ResponsiveFlex
} from '../../components/common/ResponsiveLayout';
import { combineClasses, TABLE_RESPONSIVE } from '../../utils/responsive';
import { useResponsive } from '../../hooks/useResponsive';
import { getUserImageData } from '../../utils/imageUtils';

const UserManagement = () => {
  usePageTitle('Admin | User Management');
  
  const { hasPermission } = useAdminAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    search: '',
    status: 'all',
    country: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({});
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');

  useEffect(() => {
    fetchUsers();
  }, [filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getUsers(filters);
      if (response.success) {
        setUsers(response.data.users);
        setPagination(response.data.pagination);
      } else {
        setError('Failed to load users');
      }
    } catch (error) {
      console.error('Fetch users error:', error);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    fetchUsers();
  };

  const handleSort = (sortBy) => {
    const sortOrder = filters.sortBy === sortBy && filters.sortOrder === 'desc' ? 'asc' : 'desc';
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder
    }));
  };

  const handleSelectUser = (userId) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user._id));
    }
  };

  const handleUserAction = async (action, userId, reason = '') => {
    try {
      let response;
      switch (action) {
        case 'suspend':
          response = await adminApi.suspendUser(userId, reason);
          break;
        case 'reactivate':
          response = await adminApi.reactivateUser(userId);
          break;
        case 'delete':
          response = await adminApi.deleteUser(userId);
          break;
        default:
          return;
      }

      if (response.success) {
        fetchUsers(); // Refresh the list
        setShowActionModal(false);
        setActionReason('');
        setSelectedUsers([]);
      } else {
        setError(response.message || 'Action failed');
      }
    } catch (error) {
      console.error('User action error:', error);
      setError('Action failed');
    }
  };

  const openActionModal = (action) => {
    setActionType(action);
    setShowActionModal(true);
  };

  const confirmAction = () => {
    if (selectedUsers.length === 1) {
      handleUserAction(actionType, selectedUsers[0], actionReason);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (user) => {
    const isActive = user.stats?.isActive;
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? 'Active' : 'Suspended'}
      </span>
    );
  };

  if (loading && users.length === 0) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </div>
    );
  }

  return (
    <Container className="py-4 sm:py-6">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">User Management</h1>
        <p className="text-gray-600 text-sm sm:text-base mt-1">Manage and monitor user accounts</p>
      </div>

      {error && (
        <ResponsiveCard className="mb-4 sm:mb-6 bg-red-50 border-red-200 text-red-600">
          {error}
        </ResponsiveCard>
      )}

      {/* Filters */}
      <ResponsiveCard className="mb-6 sm:mb-8">
        <form onSubmit={handleSearch} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              placeholder="Search users..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
            <input
              type="text"
              value={filters.country}
              onChange={(e) => handleFilterChange('country', e.target.value)}
              placeholder="Filter by country..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            />
          </div>
          
          <div className="flex items-end">
            <ResponsiveButton
              type="submit"
              size="medium"
              className="w-full"
            >
              Search
            </ResponsiveButton>
          </div>
        </form>
      </ResponsiveCard>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <ResponsiveCard padding="small" className="mb-6 sm:mb-8">
          <ResponsiveFlex justify="between" align="center" className="flex-wrap gap-2">
            <span className="text-sm text-gray-600">
              {selectedUsers.length} user(s) selected
            </span>
            <div className="flex flex-wrap gap-2">
              {hasPermission('users', 'suspend') && (
                <ResponsiveButton
                  onClick={() => openActionModal('suspend')}
                  variant="secondary"
                  size="small"
                  className="bg-yellow-600 hover:bg-yellow-700"
                >
                  Suspend
                </ResponsiveButton>
              )}
              {hasPermission('users', 'suspend') && (
                <ResponsiveButton
                  onClick={() => openActionModal('reactivate')}
                  variant="secondary"
                  size="small"
                  className="bg-green-600 hover:bg-green-700"
                >
                  Reactivate
                </ResponsiveButton>
              )}
              {hasPermission('users', 'delete') && (
                <ResponsiveButton
                  onClick={() => openActionModal('delete')}
                  variant="danger"
                  size="small"
                >
                  Delete
                </ResponsiveButton>
              )}
            </div>
          </ResponsiveFlex>
        </ResponsiveCard>
      )}

      {/* Users Table */}
      <ResponsiveTable>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === users.length && users.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                  />
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('firstName')}
                >
                  User
                  {filters.sortBy === 'firstName' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('email')}
                >
                  Email
                  {filters.sortBy === 'email' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stats
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('createdAt')}
                >
                  Joined
                  {filters.sortBy === 'createdAt' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user._id)}
                      onChange={() => handleSelectUser(user._id)}
                      className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        {(() => {
                          const { imageUrl, initials, hasImage } = getUserImageData(user);
                          return (
                            <>
                              {hasImage && (
                                <img
                                  className="h-10 w-10 rounded-full object-cover"
                                  src={imageUrl}
                                  alt={`${user.firstName} ${user.lastName}`}
                                  onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.nextSibling.style.display = 'flex';
                                  }}
                                />
                              )}
                              <div
                                className={`h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center ${hasImage ? 'hidden' : ''}`}
                              >
                                <span className="text-sm font-medium text-gray-700">
                                  {initials}
                                </span>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {user.firstName} {user.lastName}
                        </div>
                        <div className="text-sm text-gray-500">@{user.userName}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {user.email}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(user)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>Products: {user.stats?.totalProducts || 0}</div>
                    <div>Orders: {user.stats?.totalOrders || 0}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(user.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Link
                        to={`/admin/users/${user._id}`}
                        className="text-teal-600 hover:text-teal-900"
                      >
                        View
                      </Link>
                      {hasPermission('users', 'suspend') && user.stats?.isActive && (
                        <button
                          onClick={() => {
                            setSelectedUsers([user._id]);
                            openActionModal('suspend');
                          }}
                          className="text-yellow-600 hover:text-yellow-900"
                        >
                          Suspend
                        </button>
                      )}
                      {hasPermission('users', 'suspend') && !user.stats?.isActive && (
                        <button
                          onClick={() => handleUserAction('reactivate', user._id)}
                          className="text-green-600 hover:text-green-900"
                        >
                          Reactivate
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
      </ResponsiveTable>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <ResponsiveCard className="mt-0 rounded-t-none border-t-0">
          <div className="flex items-center justify-between">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handleFilterChange('page', filters.page - 1)}
                disabled={!pagination.hasPrev}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handleFilterChange('page', filters.page + 1)}
                disabled={!pagination.hasNext}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{pagination.currentPage}</span> of{' '}
                  <span className="font-medium">{pagination.totalPages}</span> ({pagination.totalUsers} total users)
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handleFilterChange('page', filters.page - 1)}
                    disabled={!pagination.hasPrev}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handleFilterChange('page', filters.page + 1)}
                    disabled={!pagination.hasNext}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </ResponsiveCard>
      )}

      {/* Action Modal */}
      {showActionModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Confirm {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Are you sure you want to {actionType} {selectedUsers.length} user(s)?
              </p>
              
              {(actionType === 'suspend' || actionType === 'delete') && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reason {actionType === 'suspend' ? '(optional)' : '(required)'}
                  </label>
                  <textarea
                    value={actionReason}
                    onChange={(e) => setActionReason(e.target.value)}
                    placeholder={`Enter reason for ${actionType}...`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                    rows="3"
                  />
                </div>
              )}
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowActionModal(false);
                    setActionReason('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmAction}
                  disabled={actionType === 'delete' && !actionReason}
                  className={`px-4 py-2 rounded-md text-white disabled:opacity-50 ${
                    actionType === 'delete' 
                      ? 'bg-red-600 hover:bg-red-700' 
                      : actionType === 'suspend'
                      ? 'bg-yellow-600 hover:bg-yellow-700'
                      : 'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

export default UserManagement;
