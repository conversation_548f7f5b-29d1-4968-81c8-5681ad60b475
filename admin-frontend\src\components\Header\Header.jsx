import React, { useEffect, useState } from 'react';
import { Search, Menu, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAppContext } from '../../context/AppContext';
import MegaMenu from './MegaMenu';
import HeaderCategories from './HeaderCategories';
import Logo from '../common/Logo';
import MobileMenu from './MobileMenu';

const Header = () => {
  const navigate = useNavigate();
  const {
    isMobileMenuOpen,
    setIsMobileMenuOpen
  } = useAppContext();

  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleLogin = () => {
    navigate('/admin/login');
  };

  const handleSignup = () => {
    navigate('/admin/signup');
  };

  return (
    <header className={`border-b border-gray-200 sticky top-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-sm' : 'bg-white md:bg-transparent'}`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 md:h-20">
          {/* Logo */}
          <div className="flex items-center">
            <Logo />
          </div>

          {/* Desktop Categories */}


          {/* Mobile Menu Toggle */}
          <button
            className="md:hidden flex items-center"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>

          {/* Search and Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search products"
                className="pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-teal-500 w-60"
              />
              <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
            </div>
            <button
              onClick={handleLogin}
             
              className="text-gray-700 hover:text-teal-600 font-medium"
            >
              Login
            </button>
            <button
              onClick={handleSignup}
              className="text-gray-700 hover:text-teal-600 font-medium"
            >
              Sign up
            </button>
            <button className="bg-teal-500 hover:bg-teal-600 text-white font-medium py-2 px-4 rounded-full transition-colors duration-200">
              Sell now
            </button>
          </div>
        </div>
        <div className="hidden md:block flex-grow mx-8">
          <HeaderCategories />
        </div>
          <MegaMenu />
      </div>


      {/* Mega Menu */}
    

      {/* Mobile Menu */}
      {isMobileMenuOpen && <MobileMenu />}
    </header>
  );
};

export default Header;
