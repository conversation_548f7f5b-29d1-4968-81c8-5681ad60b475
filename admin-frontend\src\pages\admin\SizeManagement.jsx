import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { Plus, Edit, Trash2, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { useNetworkError } from '../../hooks/useNetworkError';

const SizeManagement = () => {
    const networkError = useNetworkError();
    const [sizeCharts, setSizeCharts] = useState([]);
    const [childCategories, setChildCategories] = useState([]);
    const [loading, setLoading] = useState(true);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [editingSizeChart, setEditingSizeChart] = useState(null);
    const [formData, setFormData] = useState({
        childCategoryId: '',
        childCategorySlug: '',
        sizes: []
    });
    const [newSize, setNewSize] = useState('');
    
    // Pagination and search
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [searchTerm, setSearchTerm] = useState('');
    const [itemsPerPage] = useState(10);

    useEffect(() => {
        fetchSizeCharts();
        fetchChildCategories();
    }, [currentPage, searchTerm]);

    const fetchSizeCharts = async () => {
        try {
            setLoading(true);
            const params = {
                page: currentPage,
                limit: itemsPerPage,
                search: searchTerm
            };
            const response = await adminApi.getSizeCharts(params);
            setSizeCharts(response.data.sizeCharts || []);
            setTotalPages(response.data.pagination?.totalPages || 1);
        } catch (error) {
            console.error('❌ Error fetching size charts:', error);
            const wasHandled = networkError.handleApiError(error, () => fetchSizeCharts());
            if (!wasHandled) {
                alert('Failed to fetch size charts: ' + (error.response?.data?.message || error.message));
            }
        } finally {
            setLoading(false);
        }
    };

    const fetchChildCategories = async () => {
        try {
            const response = await adminApi.getChildCategories();
            setChildCategories(response.data.childCategories || []);
        } catch (error) {
            console.error('❌ Error fetching child categories:', error);
            networkError.handleApiError(error, () => fetchChildCategories());
        }
    };

    const handleAddEdit = (sizeChart = null) => {
        setEditingSizeChart(sizeChart);
        if (sizeChart) {
            setFormData({
                childCategoryId: sizeChart.childCategory._id,
                childCategorySlug: sizeChart.childCategory.slug,
                sizes: [...sizeChart.sizes]
            });
        } else {
            setFormData({
                childCategoryId: '',
                childCategorySlug: '',
                sizes: []
            });
        }
        setNewSize('');
        setIsModalVisible(true);
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this size chart?')) {
            try {
                await adminApi.deleteSizeChart(id);
                fetchSizeCharts();
                alert('Size chart deleted successfully!');
            } catch (error) {
                console.error('❌ Delete failed:', error);
                const wasHandled = networkError.handleApiError(error, () => handleDelete(id));
                if (!wasHandled) {
                    alert('Failed to delete size chart: ' + (error.response?.data?.message || error.message));
                }
            }
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!formData.childCategoryId || formData.sizes.length === 0) {
            alert('Please select a child category and add at least one size');
            return;
        }

        try {
            if (editingSizeChart) {
                await adminApi.updateSizeChart(editingSizeChart._id, formData);
            } else {
                await adminApi.createSizeChart(formData);
            }
            setIsModalVisible(false);
            fetchSizeCharts();
            alert('Size chart saved successfully!');
        } catch (error) {
            console.error('❌ Save failed:', error);
            const wasHandled = networkError.handleApiError(error, () => handleSubmit(e));
            if (!wasHandled) {
                alert('Failed to save size chart: ' + (error.response?.data?.message || error.message));
            }
        }
    };

    const handleCategoryChange = (e) => {
        const selectedId = e.target.value;
        const selectedCategory = childCategories.find(cat => cat._id === selectedId);
        
        setFormData(prev => ({
            ...prev,
            childCategoryId: selectedId,
            childCategorySlug: selectedCategory?.slug || ''
        }));
    };

    const addSize = () => {
        if (newSize.trim() && !formData.sizes.includes(newSize.trim())) {
            setFormData(prev => ({
                ...prev,
                sizes: [...prev.sizes, newSize.trim()]
            }));
            setNewSize('');
        }
    };

    const removeSize = (index) => {
        setFormData(prev => ({
            ...prev,
            sizes: prev.sizes.filter((_, i) => i !== index)
        }));
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };

    return (
        <div className="p-4 sm:p-6 lg:p-8">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Size Management</h1>
                <p className="text-gray-600">Manage size charts for different product categories</p>
            </div>

            {/* Search and Actions */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
                <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    <input
                        type="text"
                        placeholder="Search size charts..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                </div>
                <button
                    onClick={() => handleAddEdit()}
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                >
                    <Plus size={20} />
                    <span className="hidden sm:inline">Add Size Chart</span>
                    <span className="sm:hidden">Add</span>
                </button>
            </div>

            {/* Loading State */}
            {loading ? (
                <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            ) : (
                <>
                    {/* Desktop Table */}
                    <div className="hidden lg:block bg-white rounded-lg shadow overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Child Category
                                    </th>
                                    {/* <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Parent Category
                                    </th> */}
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Sizes
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Created
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {sizeCharts.map((sizeChart) => (
                                    <tr key={sizeChart._id} className="hover:bg-gray-50">
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">
                                                {sizeChart.childCategory?.name || sizeChart.childCategory.slug}
                                            </div>
                                            <div className="text-sm text-gray-500">
                                                {sizeChart.childCategory.slug}
                                            </div>
                                        </td>
                                        {/* <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            {sizeChart.parentCategory || 'N/A'}
                                        </td> */}
                                        <td className="px-6 py-4">
                                            <div className="flex flex-wrap gap-1">
                                                {sizeChart.sizes.slice(0, 3).map((size, index) => (
                                                    <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                                        {size}
                                                    </span>
                                                ))}
                                                {sizeChart.sizes.length > 3 && (
                                                    <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                                                        +{sizeChart.sizes.length - 3} more
                                                    </span>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {new Date(sizeChart.createdAt).toLocaleDateString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div className="flex gap-2">
                                                <button
                                                    onClick={() => handleAddEdit(sizeChart)}
                                                    className="text-blue-600 hover:text-blue-900 p-1"
                                                    title="Edit"
                                                >
                                                    <Edit size={16} />
                                                </button>
                                                <button
                                                    onClick={() => handleDelete(sizeChart._id)}
                                                    className="text-red-600 hover:text-red-900 p-1"
                                                    title="Delete"
                                                >
                                                    <Trash2 size={16} />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>

                    {/* Mobile Cards */}
                    <div className="lg:hidden space-y-4">
                        {sizeCharts.map((sizeChart) => (
                            <div key={sizeChart._id} className="bg-white rounded-lg shadow p-4">
                                <div className="flex justify-between items-start mb-3">
                                    <div>
                                        <h3 className="font-medium text-gray-900">
                                            {sizeChart.childCategory?.name || sizeChart.childCategory.slug}
                                        </h3>
                                        <p className="text-sm text-gray-500">{sizeChart.childCategory.slug}</p>
                                        <p className="text-xs text-gray-400 mt-1">
                                            {new Date(sizeChart.createdAt).toLocaleDateString()}
                                        </p>
                                    </div>
                                    <div className="flex gap-2">
                                        <button
                                            onClick={() => handleAddEdit(sizeChart)}
                                            className="text-blue-600 hover:text-blue-900 p-1"
                                        >
                                            <Edit size={16} />
                                        </button>
                                        <button
                                            onClick={() => handleDelete(sizeChart._id)}
                                            className="text-red-600 hover:text-red-900 p-1"
                                        >
                                            <Trash2 size={16} />
                                        </button>
                                    </div>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                    {sizeChart.sizes.slice(0, 5).map((size, index) => (
                                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                            {size}
                                        </span>
                                    ))}
                                    {sizeChart.sizes.length > 5 && (
                                        <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                                            +{sizeChart.sizes.length - 5} more
                                        </span>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="mt-6 flex justify-center items-center gap-2">
                            <button
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                <ChevronLeft size={20} />
                            </button>

                            <div className="flex gap-1">
                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                    let pageNum;
                                    if (totalPages <= 5) {
                                        pageNum = i + 1;
                                    } else if (currentPage <= 3) {
                                        pageNum = i + 1;
                                    } else if (currentPage >= totalPages - 2) {
                                        pageNum = totalPages - 4 + i;
                                    } else {
                                        pageNum = currentPage - 2 + i;
                                    }

                                    return (
                                        <button
                                            key={pageNum}
                                            onClick={() => handlePageChange(pageNum)}
                                            className={`px-3 py-2 rounded-lg ${
                                                currentPage === pageNum
                                                    ? 'bg-blue-600 text-white'
                                                    : 'border border-gray-300 hover:bg-gray-50'
                                            }`}
                                        >
                                            {pageNum}
                                        </button>
                                    );
                                })}
                            </div>

                            <button
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                            >
                                <ChevronRight size={20} />
                            </button>
                        </div>
                    )}

                    {/* Empty State */}
                    {sizeCharts.length === 0 && (
                        <div className="text-center py-12">
                            <div className="text-gray-400 mb-4">
                                <Filter size={48} className="mx-auto" />
                            </div>
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No size charts found</h3>
                            <p className="text-gray-500 mb-4">
                                {searchTerm ? 'Try adjusting your search terms' : 'Get started by creating your first size chart'}
                            </p>
                            {!searchTerm && (
                                <button
                                    onClick={() => handleAddEdit()}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
                                >
                                    Create Size Chart
                                </button>
                            )}
                        </div>
                    )}
                </>
            )}

            {/* Modal */}
            {isModalVisible && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        <div className="p-6">
                            <h2 className="text-xl font-bold mb-4">
                                {editingSizeChart ? 'Edit Size Chart' : 'Create Size Chart'}
                            </h2>

                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Child Category *
                                    </label>
                                    <select
                                        value={formData.childCategoryId}
                                        onChange={handleCategoryChange}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Select a child category</option>
                                        {childCategories.map((category) => (
                                            <option key={category._id} value={category._id}>
                                                {category.name} ({category.parentCategory} → {category.parentSubCategory})
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Add Sizes *
                                    </label>
                                    <div className="flex gap-2 mb-3">
                                        <input
                                            type="text"
                                            value={newSize}
                                            onChange={(e) => setNewSize(e.target.value)}
                                            placeholder="Enter size (e.g., XS, S, M, L, XL)"
                                            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addSize())}
                                        />
                                        <button
                                            type="button"
                                            onClick={addSize}
                                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg"
                                        >
                                            Add
                                        </button>
                                    </div>

                                    {formData.sizes.length > 0 && (
                                        <div className="border border-gray-200 rounded-lg p-3">
                                            <p className="text-sm font-medium text-gray-700 mb-2">
                                                Current Sizes ({formData.sizes.length}):
                                            </p>
                                            <div className="flex flex-wrap gap-2">
                                                {formData.sizes.map((size, index) => (
                                                    <span
                                                        key={index}
                                                        className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                                                    >
                                                        {size}
                                                        <button
                                                            type="button"
                                                            onClick={() => removeSize(index)}
                                                            className="text-blue-600 hover:text-blue-800 ml-1"
                                                        >
                                                            ×
                                                        </button>
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>

                                <div className="flex gap-3 pt-4">
                                    <button
                                        type="submit"
                                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg"
                                    >
                                        {editingSizeChart ? 'Update' : 'Create'} Size Chart
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => setIsModalVisible(false)}
                                        className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SizeManagement;
