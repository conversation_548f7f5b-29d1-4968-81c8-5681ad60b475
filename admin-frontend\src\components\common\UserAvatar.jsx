import React from 'react';

const UserAvatar = ({ 
  user, 
  size = 'md', 
  showName = false, 
  className = '',
  imageClassName = '',
  nameClassName = ''
}) => {
  // Size configurations
  const sizeClasses = {
    xs: 'w-6 h-6 text-xs',
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
    xl: 'w-16 h-16 text-lg'
  };

  // Get user initials
  const getInitials = (user) => {
    if (!user) return '??';
    
    const firstName = user.firstName || user.first_name || '';
    const lastName = user.lastName || user.last_name || user.surname || '';
    
    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    } else if (firstName) {
      return firstName.charAt(0).toUpperCase();
    } else if (user.name) {
      const nameParts = user.name.split(' ');
      if (nameParts.length >= 2) {
        return `${nameParts[0].charAt(0)}${nameParts[1].charAt(0)}`.toUpperCase();
      }
      return nameParts[0].charAt(0).toUpperCase();
    } else if (user.email) {
      return user.email.charAt(0).toUpperCase();
    }
    
    return '??';
  };

  // Get user display name
  const getDisplayName = (user) => {
    if (!user) return 'Unknown User';
    
    const firstName = user.firstName || user.first_name || '';
    const lastName = user.lastName || user.last_name || user.surname || '';
    
    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (firstName) {
      return firstName;
    } else if (user.name) {
      return user.name;
    } else if (user.email) {
      return user.email;
    }
    
    return 'Unknown User';
  };

  // Get user image URL
  const getImageUrl = (user) => {
    if (!user) return null;
    
    // Check various possible image field names
    const imageFields = [
      'profileImage',
      'profile_image',
      'avatar',
      'image',
      'photo',
      'picture'
    ];
    
    for (const field of imageFields) {
      if (user[field]) {
        // Handle relative URLs
        if (user[field].startsWith('/')) {
          return `http://localhost:5001${user[field]}`;
        }
        return user[field];
      }
    }
    
    return null;
  };

  const initials = getInitials(user);
  const displayName = getDisplayName(user);
  const imageUrl = getImageUrl(user);
  const sizeClass = sizeClasses[size] || sizeClasses.md;

  // Generate a consistent color based on user ID or name
  const getAvatarColor = (user) => {
    if (!user) return 'bg-gray-500';
    
    const id = user._id || user.id || user.email || user.name || 'default';
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-teal-500'
    ];
    
    let hash = 0;
    for (let i = 0; i < id.length; i++) {
      hash = id.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };

  const avatarColor = getAvatarColor(user);

  return (
    <div className={`flex items-center ${className}`}>
      <div className={`relative ${sizeClass} rounded-full flex-shrink-0 ${imageClassName}`}>
        {imageUrl ? (
          <img
            src={imageUrl}
            alt={displayName}
            className={`w-full h-full rounded-full object-cover`}
            onError={(e) => {
              // Hide image and show initials on error
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
        ) : null}
        
        <div 
          className={`${imageUrl ? 'hidden' : 'flex'} w-full h-full rounded-full ${avatarColor} items-center justify-center text-white font-medium`}
          style={{ display: imageUrl ? 'none' : 'flex' }}
        >
          {initials}
        </div>
      </div>
      
      {showName && (
        <div className={`ml-3 ${nameClassName}`}>
          <p className="text-sm font-medium text-gray-900 truncate">
            {displayName}
          </p>
          {user?.email && (
            <p className="text-xs text-gray-500 truncate">
              {user.email}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default UserAvatar;
