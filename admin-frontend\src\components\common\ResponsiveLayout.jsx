import React from 'react';
import { RESPONSIVE_CLASSES, combineClasses } from '../../utils/responsive';

// Main container component
export const Container = ({ children, size = 'default', className = '' }) => {
  const sizeClasses = {
    small: 'max-w-4xl',
    default: 'max-w-7xl',
    large: 'max-w-full',
    full: 'max-w-none'
  };

  return (
    <div className={combineClasses(
      'w-full mx-auto px-4 sm:px-6 lg:px-8',
      sizeClasses[size],
      className
    )}>
      {children}
    </div>
  );
};

// Responsive grid component
export const ResponsiveGrid = ({ 
  children, 
  cols = 3, 
  gap = 'medium', 
  className = '' 
}) => {
  const colClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5',
    6: 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6'
  };

  const gapClasses = {
    small: 'gap-2 sm:gap-4',
    medium: 'gap-4 sm:gap-6',
    large: 'gap-6 sm:gap-8'
  };

  return (
    <div className={combineClasses(
      'grid',
      colClasses[cols] || colClasses[3],
      gapClasses[gap] || gapClasses.medium,
      className
    )}>
      {children}
    </div>
  );
};

// Responsive card component
export const ResponsiveCard = ({ 
  children, 
  padding = 'medium', 
  className = '',
  hover = false 
}) => {
  const paddingClasses = {
    small: 'p-3 sm:p-4',
    medium: 'p-4 sm:p-6',
    large: 'p-6 sm:p-8'
  };

  return (
    <div className={combineClasses(
      'bg-white rounded-lg shadow-sm border',
      paddingClasses[padding] || paddingClasses.medium,
      hover ? 'hover:shadow-md transition-shadow duration-200' : '',
      className
    )}>
      {children}
    </div>
  );
};

// Responsive flex container
export const ResponsiveFlex = ({ 
  children, 
  direction = 'row', 
  align = 'start', 
  justify = 'start',
  gap = 'medium',
  wrap = true,
  className = '' 
}) => {
  const directionClasses = {
    row: 'flex-col sm:flex-row',
    'row-reverse': 'flex-col-reverse sm:flex-row-reverse',
    col: 'flex-col',
    'col-reverse': 'flex-col-reverse'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around'
  };

  const gapClasses = {
    small: 'gap-2 sm:gap-3',
    medium: 'gap-3 sm:gap-4',
    large: 'gap-4 sm:gap-6'
  };

  return (
    <div className={combineClasses(
      'flex',
      directionClasses[direction] || directionClasses.row,
      alignClasses[align] || alignClasses.start,
      justifyClasses[justify] || justifyClasses.start,
      gapClasses[gap] || gapClasses.medium,
      wrap ? 'flex-wrap' : 'flex-nowrap',
      className
    )}>
      {children}
    </div>
  );
};

// Responsive button component
export const ResponsiveButton = ({ 
  children, 
  size = 'medium', 
  variant = 'primary',
  fullWidth = false,
  className = '',
  ...props 
}) => {
  const sizeClasses = {
    small: 'px-3 py-1.5 text-sm sm:px-4 sm:py-2',
    medium: 'px-4 py-2 text-sm sm:px-6 sm:py-3 sm:text-base',
    large: 'px-6 py-3 text-base sm:px-8 sm:py-4 sm:text-lg'
  };

  const variantClasses = {
    primary: 'bg-teal-600 hover:bg-teal-700 text-white',
    secondary: 'bg-gray-600 hover:bg-gray-700 text-white',
    outline: 'border border-teal-600 text-teal-600 hover:bg-teal-50',
    danger: 'bg-red-600 hover:bg-red-700 text-white'
  };

  return (
    <button
      className={combineClasses(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500',
        sizeClasses[size] || sizeClasses.medium,
        variantClasses[variant] || variantClasses.primary,
        fullWidth ? 'w-full' : '',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};

// Responsive table wrapper
export const ResponsiveTable = ({ children, className = '' }) => {
  return (
    <div className={combineClasses(
      'overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg',
      className
    )}>
      <table className="min-w-full divide-y divide-gray-200">
        {children}
      </table>
    </div>
  );
};

// Responsive form grid
export const ResponsiveFormGrid = ({ 
  children, 
  cols = 2, 
  gap = 'medium',
  className = '' 
}) => {
  const colClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
  };

  const gapClasses = {
    small: 'gap-3 sm:gap-4',
    medium: 'gap-4 sm:gap-6',
    large: 'gap-6 sm:gap-8'
  };

  return (
    <div className={combineClasses(
      'grid',
      colClasses[cols] || colClasses[2],
      gapClasses[gap] || gapClasses.medium,
      className
    )}>
      {children}
    </div>
  );
};

// Responsive input component
export const ResponsiveInput = ({ 
  label, 
  error, 
  className = '',
  fullWidth = true,
  ...props 
}) => {
  return (
    <div className={fullWidth ? 'w-full' : ''}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1 sm:mb-2">
          {label}
        </label>
      )}
      <input
        className={combineClasses(
          'w-full px-3 py-2 text-sm sm:text-base border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500',
          error ? 'border-red-300' : 'border-gray-300',
          className
        )}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

// Responsive modal component
export const ResponsiveModal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  size = 'medium',
  className = '' 
}) => {
  if (!isOpen) return null;

  const sizeClasses = {
    small: 'max-w-md',
    medium: 'max-w-lg sm:max-w-2xl',
    large: 'max-w-2xl sm:max-w-4xl',
    xlarge: 'max-w-4xl sm:max-w-6xl'
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="fixed inset-0 bg-black bg-opacity-50" onClick={onClose}></div>
      <div className="relative min-h-screen flex items-center justify-center p-4">
        <div className={combineClasses(
          'relative bg-white rounded-lg shadow-xl w-full',
          sizeClasses[size] || sizeClasses.medium,
          className
        )}>
          {title && (
            <div className="px-4 py-3 sm:px-6 sm:py-4 border-b border-gray-200">
              <h3 className="text-lg sm:text-xl font-medium text-gray-900">
                {title}
              </h3>
            </div>
          )}
          <div className="px-4 py-3 sm:px-6 sm:py-4">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

// Responsive stats card
export const ResponsiveStatsCard = ({ 
  title, 
  value, 
  change, 
  icon, 
  className = '' 
}) => {
  return (
    <ResponsiveCard className={combineClasses('relative', className)}>
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-500 truncate">
            {title}
          </p>
          <p className="text-2xl sm:text-3xl font-bold text-gray-900">
            {value}
          </p>
          {change && (
            <p className={combineClasses(
              'text-sm font-medium',
              change.startsWith('+') ? 'text-green-600' : 'text-red-600'
            )}>
              {change}
            </p>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0">
            <div className="w-8 h-8 sm:w-10 sm:h-10 text-teal-600">
              {icon}
            </div>
          </div>
        )}
      </div>
    </ResponsiveCard>
  );
};
