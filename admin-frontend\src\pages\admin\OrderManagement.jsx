import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Calendar,
  Package,
  CreditCard,
  User,
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import ResponsiveCard from '../../components/common/ResponsiveCard';
import ResponsiveGrid from '../../components/common/ResponsiveGrid';
import ResponsiveStatsCard from '../../components/common/ResponsiveStatsCard';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';
import UserAvatar from '../../components/common/UserAvatar';
import {
  ResponsiveTable,
  ResponsiveTableHeader,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell,
  MobileCard,
  MobileCardHeader,
  MobileCardContent,
  MobileCardField,
  MobileCardActions
} from '../../components/common/ResponsiveTable';
import { adminApi } from '../../services/adminApi';

const OrderManagement = () => {
  const [orders, setOrders] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'escrow', 'standard'
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    search: '',
    status: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    dateFrom: '',
    dateTo: ''
  });
  const [pagination, setPagination] = useState({});

  // Load orders based on active tab
  const loadOrders = async () => {
    try {
      setLoading(true);
      let response;

      if (activeTab === 'all') {
        response = await adminApi.getAllOrders(filters);
      } else {
        response = await adminApi.getOrdersByMethod(activeTab, filters);
      }

      if (response && response.success) {
        setOrders(response.data.orders || []);
        setPagination(response.data.pagination || {});
      } else {
        console.warn('Invalid response format:', response);
        setOrders([]);
        setPagination({});
      }
    } catch (error) {
      console.error('Error loading orders:', error);
      // Don't show error toast on initial load to prevent spam
      if (orders.length === 0) {
        console.warn('Failed to load orders on initial load');
      } else {
        toast.error('Failed to load orders');
      }
      setOrders([]);
      setPagination({});
    } finally {
      setLoading(false);
    }
  };

  // Load order statistics
  const loadStats = async () => {
    try {
      const response = await adminApi.getOrderStats();
      if (response && response.success) {
        setStats(response.data || {});
      } else {
        console.warn('Invalid stats response format:', response);
        setStats({});
      }
    } catch (error) {
      console.error('Error loading order stats:', error);
      setStats({});
    }
  };

  useEffect(() => {
    // Add a small delay to ensure component is mounted
    const timer = setTimeout(() => {
      loadOrders();
    }, 100);

    return () => clearTimeout(timer);
  }, [activeTab, filters]);

  useEffect(() => {
    // Add a small delay to ensure component is mounted
    const timer = setTimeout(() => {
      loadStats();
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setFilters(prev => ({ ...prev, page: 1 }));
  };

  // Format currency
  const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    const colors = {
      'pending_payment': 'bg-yellow-100 text-yellow-800',
      'paid': 'bg-green-100 text-green-800',
      'processing': 'bg-blue-100 text-blue-800',
      'shipped': 'bg-purple-100 text-purple-800',
      'delivered': 'bg-green-100 text-green-800',
      'cancelled': 'bg-red-100 text-red-800',
      'refunded': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Order Management</h1>
          <p className="text-gray-600 mt-1">Manage and monitor all orders</p>
        </div>
        <button
          onClick={() => {
            loadOrders();
            loadStats();
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Statistics */}
      <ResponsiveGrid cols={4} gap="medium">
        <ResponsiveStatsCard
          title="Total Orders"
          value={stats.totalOrders || 0}
          icon={<Package className="w-full h-full text-blue-600" />}
        />
        <ResponsiveStatsCard
          title="Escrow Orders"
          value={stats.paymentMethods?.escrow || 0}
          icon={<CreditCard className="w-full h-full text-green-600" />}
        />
        <ResponsiveStatsCard
          title="Standard Orders"
          value={stats.paymentMethods?.standard || 0}
          icon={<CreditCard className="w-full h-full text-purple-600" />}
        />
        <ResponsiveStatsCard
          title="Recent Orders"
          value={stats.recentOrders || 0}
          change="Last 30 days"
          icon={<TrendingUp className="w-full h-full text-orange-600" />}
        />
      </ResponsiveGrid>

      {/* Tabs */}
      <ResponsiveCard>
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'all', label: 'All Orders', count: stats.totalOrders },
              { key: 'escrow', label: 'Escrow Orders', count: stats.paymentMethods?.escrow },
              { key: 'standard', label: 'Standard Orders', count: stats.paymentMethods?.standard }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => handleTabChange(tab.key)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
                {tab.count !== undefined && (
                  <span className="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search orders..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              <option value="pending_payment">Pending Payment</option>
              <option value="paid">Paid</option>
              <option value="processing">Processing</option>
              <option value="shipped">Shipped</option>
              <option value="delivered">Delivered</option>
              <option value="cancelled">Cancelled</option>
              <option value="refunded">Refunded</option>
            </select>

            {/* Date From */}
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />

            {/* Date To */}
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Orders Table */}
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner />
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No orders found</h3>
              <p className="mt-1 text-sm text-gray-500">
                No orders match your current filters.
              </p>
            </div>
          ) : (
            <ResponsiveTable>
              <ResponsiveTableHeader>
                <tr>
                  <ResponsiveTableCell header>Order</ResponsiveTableCell>
                  <ResponsiveTableCell header>Customer</ResponsiveTableCell>
                  <ResponsiveTableCell header>Seller</ResponsiveTableCell>
                  <ResponsiveTableCell header>Product</ResponsiveTableCell>
                  <ResponsiveTableCell header>Amount</ResponsiveTableCell>
                  <ResponsiveTableCell header>Status</ResponsiveTableCell>
                  <ResponsiveTableCell header>Date</ResponsiveTableCell>
                  <ResponsiveTableCell header>Actions</ResponsiveTableCell>
                </tr>
              </ResponsiveTableHeader>
              <ResponsiveTableBody>
                {orders.map((order) => (
                  <ResponsiveTableRow
                    key={order._id}
                    mobileCard={
                      <MobileCard>
                        <MobileCardHeader>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {order.orderNumber}
                            </div>
                            <div className="text-xs text-gray-500">
                              {order.payment.method === 'escrow' ? 'Escrow' : 'Standard'}
                            </div>
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(order.status || 'pending')}`}>
                            {(order.status || 'pending').replace('_', ' ')}
                          </span>
                        </MobileCardHeader>

                        <MobileCardContent>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">Customer:</span>
                            <UserAvatar user={order.buyer} size="sm" showName />
                          </div>

                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">Seller:</span>
                            <UserAvatar user={order.seller} size="sm" showName />
                          </div>

                          <MobileCardField
                            label="Product"
                            value={order.product?.title || 'N/A'}
                          />

                          <MobileCardField
                            label="Amount"
                            value={formatCurrency(order.orderDetails.productPrice, order.orderDetails.currency)}
                          />

                          <MobileCardField
                            label="Date"
                            value={formatDate(order.createdAt)}
                          />
                        </MobileCardContent>

                        <MobileCardActions>
                          <button
                            onClick={() => window.open(`/admin/orders/${order._id || 'unknown'}`, '_blank')}
                            className="text-blue-600 hover:text-blue-900 p-1"
                            title="View Order"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => {/* Handle edit order status */}}
                            className="text-green-600 hover:text-green-900 p-1"
                            title="Edit Order"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                        </MobileCardActions>
                      </MobileCard>
                    }
                  >
                    <ResponsiveTableCell>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {order.orderNumber}
                        </div>
                        <div className="text-sm text-gray-500">
                          {order.payment.method === 'escrow' ? 'Escrow' : 'Standard'}
                        </div>
                      </div>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <UserAvatar user={order.buyer} showName />
                    </ResponsiveTableCell>
                    <ResponsiveTableCell>
                      <UserAvatar user={order.seller} showName />
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="text-sm font-medium text-gray-900">
                        {order.product?.title || 'N/A'}
                      </div>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(order.orderDetails.productPrice, order.orderDetails.currency)}
                      </div>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(order.status || 'pending')}`}>
                        {(order.status || 'pending').replace('_', ' ')}
                      </span>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell className="text-sm text-gray-500">
                      {formatDate(order.createdAt)}
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => window.open(`/admin/orders/${order._id || 'unknown'}`, '_blank')}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Order"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {/* Handle edit order status */}}
                          className="text-green-600 hover:text-green-900"
                          title="Edit Order"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </ResponsiveTableCell>
                  </ResponsiveTableRow>
                ))}
              </ResponsiveTableBody>
            </ResponsiveTable>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </ResponsiveCard>
    </div>
  );
};

export default OrderManagement;
