import React, { useRef, useEffect } from 'react';
import { useAppContext } from '../../context/AppContext';
import { categories } from '../../data/categories';

const MegaMenu = () => {
  const {
    isMegaMenuOpen,
    setIsMegaMenuOpen,
    activeCategory
  } = useAppContext();

  const megaMenuRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target)) {
        setIsMegaMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setIsMegaMenuOpen]);

  const activeMenu = categories.find(cat => cat.id === activeCategory);

  if (!isMegaMenuOpen || !activeMenu) return null;

  return (
    <div
      ref={megaMenuRef}
      className="absolute left-0 w-full bg-white shadow-lg border-t border-gray-100"
      onMouseLeave={() => setIsMegaMenuOpen(false)}
    >
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-4 gap-6">
          {activeMenu.subcategories.map((subcategory) => (
            <div key={subcategory.id} className="space-y-3">
              <h3 className="font-semibold text-gray-800">{subcategory.name}</h3>
              <ul className="space-y-2">
                {subcategory.items.map((item) => (
                  <li key={item.id}>
                    <a
                      href="#"
                      className="text-sm text-gray-600 hover:text-teal-600 transition-colors duration-200"
                    >
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MegaMenu;
