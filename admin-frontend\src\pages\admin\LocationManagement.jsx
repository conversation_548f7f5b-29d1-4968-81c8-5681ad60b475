import React, { useState, useEffect } from 'react';
import { adminApi } from '../../services/adminApi';
import { Plus, Edit, Trash2, Search, Globe, MapPin, ChevronLeft, ChevronRight, Flag } from 'lucide-react';
import { useNetworkError } from '../../hooks/useNetworkError';

const LocationManagement = () => {
    const networkError = useNetworkError();
    const [activeTab, setActiveTab] = useState('countries');
    const [loading, setLoading] = useState(true);
    
    // Countries state
    const [countries, setCountries] = useState([]);
    const [countryStats, setCountryStats] = useState({});
    const [countryCurrentPage, setCountryCurrentPage] = useState(1);
    const [countryTotalPages, setCountryTotalPages] = useState(1);
    const [countrySearchTerm, setCountrySearchTerm] = useState('');
    
    // Cities state
    const [cities, setCities] = useState([]);
    const [cityStats, setCityStats] = useState({});
    const [cityCurrentPage, setCityCurrentPage] = useState(1);
    const [cityTotalPages, setCityTotalPages] = useState(1);
    const [citySearchTerm, setCitySearchTerm] = useState('');
    const [selectedCountryFilter, setSelectedCountryFilter] = useState('');
    
    // Modal state
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [modalType, setModalType] = useState(''); // 'country' or 'city'
    const [editingItem, setEditingItem] = useState(null);
    const [formData, setFormData] = useState({});
    
    const itemsPerPage = 10;

    useEffect(() => {
        if (activeTab === 'countries') {
            fetchCountries();
            fetchCountryStats();
        } else {
            fetchCities();
            fetchCityStats();
            // Also fetch countries for the dropdown in city form
            if (countries.length === 0) {
                fetchCountriesForDropdown();
            }
        }
    }, [activeTab, countryCurrentPage, countrySearchTerm, cityCurrentPage, citySearchTerm, selectedCountryFilter]);

    // Fetch countries for dropdown (without pagination)
    const fetchCountriesForDropdown = async () => {
        try {
            const response = await adminApi.getCountries({ limit: 1000, sortBy: 'name', sortOrder: 'asc' });
            setCountries(response.data.countries || []);
        } catch (error) {
            console.error('❌ Error fetching countries for dropdown:', error);
            networkError.handleApiError(error, () => fetchCountriesForDropdown());
        }
    };

    // Country functions
    const fetchCountries = async () => {
        try {
            setLoading(true);
            const params = {
                page: countryCurrentPage,
                limit: itemsPerPage,
                search: countrySearchTerm,
                sortBy: 'name',
                sortOrder: 'asc'
            };
            const response = await adminApi.getCountries(params);
            setCountries(response.data.countries || []);
            setCountryTotalPages(response.data.pagination?.totalPages || 1);
        } catch (error) {
            console.error('❌ Error fetching countries:', error);
            const wasHandled = networkError.handleApiError(error, () => fetchCountries());
            if (!wasHandled) {
                alert('Failed to fetch countries: ' + (error.response?.data?.message || error.message));
            }
        } finally {
            setLoading(false);
        }
    };

    const fetchCountryStats = async () => {
        try {
            const response = await adminApi.getCountryStats();
            setCountryStats(response.data.stats || {});
        } catch (error) {
            console.error('❌ Error fetching country stats:', error);
            networkError.handleApiError(error, () => fetchCountryStats());
        }
    };

    // City functions
    const fetchCities = async () => {
        try {
            setLoading(true);
            const params = {
                page: cityCurrentPage,
                limit: itemsPerPage,
                search: citySearchTerm,
                country: selectedCountryFilter,
                sortBy: 'name',
                sortOrder: 'asc'
            };
            const response = await adminApi.getCities(params);
            setCities(response.data.cities || []);
            setCityTotalPages(response.data.pagination?.totalPages || 1);
        } catch (error) {
            console.error('❌ Error fetching cities:', error);
            const wasHandled = networkError.handleApiError(error, () => fetchCities());
            if (!wasHandled) {
                alert('Failed to fetch cities: ' + (error.response?.data?.message || error.message));
            }
        } finally {
            setLoading(false);
        }
    };

    const fetchCityStats = async () => {
        try {
            const response = await adminApi.getCityStats();
            setCityStats(response.data.stats || {});
        } catch (error) {
            console.error('❌ Error fetching city stats:', error);
            networkError.handleApiError(error, () => fetchCityStats());
        }
    };

    // Modal functions
    const openModal = (type, item = null) => {
        setModalType(type);
        setEditingItem(item);
        
        if (type === 'country') {
            setFormData(item ? {
                name: item.name,
                code: item.code,
                dialCode: item.dialCode,
                flag: item.flag || '',
                currencyCode: item.currency?.code || '',
                currencyName: item.currency?.name || '',
                currencySymbol: item.currency?.symbol || '',
                isActive: item.isActive,
                sortOrder: item.sortOrder || 0
            } : {
                name: '',
                code: '',
                dialCode: '',
                flag: '',
                currencyCode: '',
                currencyName: '',
                currencySymbol: '',
                isActive: true,
                sortOrder: 0
            });
        } else {
            setFormData(item ? {
                name: item.name,
                country: item.country?._id || '',
                state: item.state || '',
                latitude: item.latitude || '',
                longitude: item.longitude || '',
                population: item.population || 0,
                isCapital: item.isCapital || false,
                isActive: item.isActive,
                sortOrder: item.sortOrder || 0
            } : {
                name: '',
                country: '',
                state: '',
                latitude: '',
                longitude: '',
                population: 0,
                isCapital: false,
                isActive: true,
                sortOrder: 0
            });
        }
        
        setIsModalVisible(true);
    };

    const closeModal = () => {
        setIsModalVisible(false);
        setModalType('');
        setEditingItem(null);
        setFormData({});
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            if (modalType === 'country') {
                const countryData = {
                    name: formData.name,
                    code: formData.code,
                    dialCode: formData.dialCode,
                    flag: formData.flag,
                    currency: {
                        code: formData.currencyCode,
                        name: formData.currencyName,
                        symbol: formData.currencySymbol
                    },
                    isActive: formData.isActive,
                    sortOrder: parseInt(formData.sortOrder) || 0
                };
                
                if (editingItem) {
                    await adminApi.updateCountry(editingItem._id, countryData);
                } else {
                    await adminApi.createCountry(countryData);
                }
                
                fetchCountries();
                fetchCountryStats();
            } else {
                const cityData = {
                    name: formData.name,
                    country: formData.country,
                    state: formData.state,
                    latitude: formData.latitude ? parseFloat(formData.latitude) : null,
                    longitude: formData.longitude ? parseFloat(formData.longitude) : null,
                    population: parseInt(formData.population) || 0,
                    isCapital: formData.isCapital,
                    isActive: formData.isActive,
                    sortOrder: parseInt(formData.sortOrder) || 0
                };
                
                if (editingItem) {
                    await adminApi.updateCity(editingItem._id, cityData);
                } else {
                    await adminApi.createCity(cityData);
                }
                
                fetchCities();
                fetchCityStats();
            }
            
            closeModal();
            alert(`${modalType === 'country' ? 'Country' : 'City'} ${editingItem ? 'updated' : 'created'} successfully!`);
            
        } catch (error) {
            console.error(`❌ Error ${editingItem ? 'updating' : 'creating'} ${modalType}:`, error);
            const wasHandled = networkError.handleApiError(error, () => handleSubmit(e));
            if (!wasHandled) {
                alert(`Failed to ${editingItem ? 'update' : 'create'} ${modalType}: ` + (error.response?.data?.message || error.message));
            }
        }
    };

    const handleDelete = async (type, id, name) => {
        if (window.confirm(`Are you sure you want to delete this ${type}: ${name}?`)) {
            try {
                if (type === 'country') {
                    await adminApi.deleteCountry(id);
                    fetchCountries();
                    fetchCountryStats();
                } else {
                    await adminApi.deleteCity(id);
                    fetchCities();
                    fetchCityStats();
                }
                alert(`${type === 'country' ? 'Country' : 'City'} deleted successfully!`);
            } catch (error) {
                console.error(`❌ Delete ${type} failed:`, error);
                const wasHandled = networkError.handleApiError(error, () => handleDelete(type, id, name));
                if (!wasHandled) {
                    alert(`Failed to delete ${type}: ` + (error.response?.data?.message || error.message));
                }
            }
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSearchChange = (value, type) => {
        if (type === 'country') {
            setCountrySearchTerm(value);
            setCountryCurrentPage(1);
        } else {
            setCitySearchTerm(value);
            setCityCurrentPage(1);
        }
    };

    const handlePageChange = (page, type) => {
        if (type === 'country') {
            setCountryCurrentPage(page);
        } else {
            setCityCurrentPage(page);
        }
    };

    return (
        <div className="p-4 sm:p-6 lg:p-8">
            {/* Header */}
            <div className="mb-6">
                <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Location Management</h1>
                <p className="text-gray-600">Manage countries and cities for the platform</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {activeTab === 'countries' ? (
                    <>
                        <div className="bg-white rounded-lg shadow p-4">
                            <div className="flex items-center">
                                <Globe className="h-8 w-8 text-blue-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-500">Total Countries</p>
                                    <p className="text-2xl font-semibold text-gray-900">{countryStats.totalCountries || 0}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg shadow p-4">
                            <div className="flex items-center">
                                <Flag className="h-8 w-8 text-green-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-500">Active Countries</p>
                                    <p className="text-2xl font-semibold text-gray-900">{countryStats.activeCountries || 0}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg shadow p-4">
                            <div className="flex items-center">
                                <Flag className="h-8 w-8 text-red-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-500">Inactive Countries</p>
                                    <p className="text-2xl font-semibold text-gray-900">{countryStats.inactiveCountries || 0}</p>
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    <>
                        <div className="bg-white rounded-lg shadow p-4">
                            <div className="flex items-center">
                                <MapPin className="h-8 w-8 text-blue-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-500">Total Cities</p>
                                    <p className="text-2xl font-semibold text-gray-900">{cityStats.totalCities || 0}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg shadow p-4">
                            <div className="flex items-center">
                                <MapPin className="h-8 w-8 text-green-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-500">Active Cities</p>
                                    <p className="text-2xl font-semibold text-gray-900">{cityStats.activeCities || 0}</p>
                                </div>
                            </div>
                        </div>
                        <div className="bg-white rounded-lg shadow p-4">
                            <div className="flex items-center">
                                <MapPin className="h-8 w-8 text-yellow-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-500">Capital Cities</p>
                                    <p className="text-2xl font-semibold text-gray-900">{cityStats.capitalCities || 0}</p>
                                </div>
                            </div>
                        </div>
                    </>
                )}
            </div>

            {/* Tabs */}
            <div className="mb-6">
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        <button
                            onClick={() => setActiveTab('countries')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'countries'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            <Globe className="inline-block w-4 h-4 mr-2" />
                            Countries
                        </button>
                        <button
                            onClick={() => setActiveTab('cities')}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === 'cities'
                                    ? 'border-blue-500 text-blue-600'
                                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                        >
                            <MapPin className="inline-block w-4 h-4 mr-2" />
                            Cities
                        </button>
                    </nav>
                </div>
            </div>

            {/* Search and Actions */}
            <div className="mb-6 flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
                <div className="flex flex-col sm:flex-row gap-4 flex-1">
                    <div className="relative flex-1 max-w-md">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                        <input
                            type="text"
                            placeholder={`Search ${activeTab}...`}
                            value={activeTab === 'countries' ? countrySearchTerm : citySearchTerm}
                            onChange={(e) => handleSearchChange(e.target.value, activeTab)}
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>

                    {activeTab === 'cities' && (
                        <select
                            value={selectedCountryFilter}
                            onChange={(e) => setSelectedCountryFilter(e.target.value)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value="">All Countries</option>
                            {countries.map((country) => (
                                <option key={country._id} value={country._id}>
                                    {country.flag} {country.name}
                                </option>
                            ))}
                        </select>
                    )}
                </div>

                <button
                    onClick={() => openModal(activeTab === 'countries' ? 'country' : 'city')}
                    className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
                >
                    <Plus size={20} />
                    <span className="hidden sm:inline">Add {activeTab === 'countries' ? 'Country' : 'City'}</span>
                    <span className="sm:hidden">Add</span>
                </button>
            </div>

            {/* Loading State */}
            {loading ? (
                <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            ) : (
                <>
                    {activeTab === 'countries' ? (
                        <CountriesTable
                            countries={countries}
                            onEdit={(country) => openModal('country', country)}
                            onDelete={(id, name) => handleDelete('country', id, name)}
                        />
                    ) : (
                        <CitiesTable
                            cities={cities}
                            onEdit={(city) => openModal('city', city)}
                            onDelete={(id, name) => handleDelete('city', id, name)}
                        />
                    )}

                    {/* Pagination */}
                    <Pagination
                        currentPage={activeTab === 'countries' ? countryCurrentPage : cityCurrentPage}
                        totalPages={activeTab === 'countries' ? countryTotalPages : cityTotalPages}
                        onPageChange={(page) => handlePageChange(page, activeTab)}
                    />

                    {/* Empty State */}
                    {((activeTab === 'countries' && countries.length === 0) ||
                      (activeTab === 'cities' && cities.length === 0)) && (
                        <EmptyState
                            type={activeTab}
                            searchTerm={activeTab === 'countries' ? countrySearchTerm : citySearchTerm}
                            onAdd={() => openModal(activeTab === 'countries' ? 'country' : 'city')}
                        />
                    )}
                </>
            )}

            {/* Modal */}
            {isModalVisible && (
                <Modal
                    type={modalType}
                    editingItem={editingItem}
                    formData={formData}
                    countries={countries}
                    onInputChange={handleInputChange}
                    onSubmit={handleSubmit}
                    onClose={closeModal}
                />
            )}
        </div>
    );
};

// Countries Table Component
const CountriesTable = ({ countries, onEdit, onDelete }) => (
    <>
        {/* Desktop Table */}
        <div className="hidden lg:block bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                    <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Country
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Code
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Currency
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {countries.map((country) => (
                        <tr key={country._id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                    <span className="text-2xl mr-3">{country.flag}</span>
                                    <div>
                                        <div className="text-sm font-medium text-gray-900">{country.name}</div>
                                        <div className="text-sm text-gray-500">{country.dialCode}</div>
                                    </div>
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {country.code}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">{country.currency?.name}</div>
                                <div className="text-sm text-gray-500">{country.currency?.code} ({country.currency?.symbol})</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    country.isActive
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                }`}>
                                    {country.isActive ? 'Active' : 'Inactive'}
                                </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(country.createdAt).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => onEdit(country)}
                                        className="text-blue-600 hover:text-blue-900 p-1"
                                        title="Edit"
                                    >
                                        <Edit size={16} />
                                    </button>
                                    <button
                                        onClick={() => onDelete(country._id, country.name)}
                                        className="text-red-600 hover:text-red-900 p-1"
                                        title="Delete"
                                    >
                                        <Trash2 size={16} />
                                    </button>
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden space-y-4">
            {countries.map((country) => (
                <div key={country._id} className="bg-white rounded-lg shadow p-4">
                    <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center">
                            <span className="text-2xl mr-3">{country.flag}</span>
                            <div>
                                <h3 className="font-medium text-gray-900">{country.name}</h3>
                                <p className="text-sm text-gray-500">{country.code} • {country.dialCode}</p>
                                <p className="text-xs text-gray-400 mt-1">
                                    {new Date(country.createdAt).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                        <div className="flex gap-2">
                            <button
                                onClick={() => onEdit(country)}
                                className="text-blue-600 hover:text-blue-900 p-1"
                            >
                                <Edit size={16} />
                            </button>
                            <button
                                onClick={() => onDelete(country._id, country.name)}
                                className="text-red-600 hover:text-red-900 p-1"
                            >
                                <Trash2 size={16} />
                            </button>
                        </div>
                    </div>
                    <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600">
                            {country.currency?.name} ({country.currency?.code} {country.currency?.symbol})
                        </div>
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            country.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                        }`}>
                            {country.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            ))}
        </div>
    </>
);

// Cities Table Component
const CitiesTable = ({ cities, onEdit, onDelete }) => (
    <>
        {/* Desktop Table */}
        <div className="hidden lg:block bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                    <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            City
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Country
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            State
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Population
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {cities.map((city) => (
                        <tr key={city._id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                    <div>
                                        <div className="text-sm font-medium text-gray-900 flex items-center">
                                            {city.name}
                                            {city.isCapital && (
                                                <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                                    Capital
                                                </span>
                                            )}
                                        </div>
                                        {city.latitude && city.longitude && (
                                            <div className="text-sm text-gray-500">
                                                {city.latitude.toFixed(4)}, {city.longitude.toFixed(4)}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                    <span className="text-lg mr-2">{city.country?.flag}</span>
                                    <div>
                                        <div className="text-sm text-gray-900">{city.country?.name}</div>
                                        <div className="text-sm text-gray-500">{city.country?.code}</div>
                                    </div>
                                </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {city.state || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {city.population ? city.population.toLocaleString() : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                    city.isActive
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-red-100 text-red-800'
                                }`}>
                                    {city.isActive ? 'Active' : 'Inactive'}
                                </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div className="flex gap-2">
                                    <button
                                        onClick={() => onEdit(city)}
                                        className="text-blue-600 hover:text-blue-900 p-1"
                                        title="Edit"
                                    >
                                        <Edit size={16} />
                                    </button>
                                    <button
                                        onClick={() => onDelete(city._id, city.name)}
                                        className="text-red-600 hover:text-red-900 p-1"
                                        title="Delete"
                                    >
                                        <Trash2 size={16} />
                                    </button>
                                </div>
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>

        {/* Mobile Cards */}
        <div className="lg:hidden space-y-4">
            {cities.map((city) => (
                <div key={city._id} className="bg-white rounded-lg shadow p-4">
                    <div className="flex justify-between items-start mb-3">
                        <div>
                            <div className="flex items-center">
                                <h3 className="font-medium text-gray-900">{city.name}</h3>
                                {city.isCapital && (
                                    <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                        Capital
                                    </span>
                                )}
                            </div>
                            <div className="flex items-center mt-1">
                                <span className="text-lg mr-2">{city.country?.flag}</span>
                                <p className="text-sm text-gray-500">{city.country?.name}</p>
                            </div>
                            {city.state && (
                                <p className="text-sm text-gray-500">State: {city.state}</p>
                            )}
                            {city.population > 0 && (
                                <p className="text-sm text-gray-500">Population: {city.population.toLocaleString()}</p>
                            )}
                        </div>
                        <div className="flex gap-2">
                            <button
                                onClick={() => onEdit(city)}
                                className="text-blue-600 hover:text-blue-900 p-1"
                            >
                                <Edit size={16} />
                            </button>
                            <button
                                onClick={() => onDelete(city._id, city.name)}
                                className="text-red-600 hover:text-red-900 p-1"
                            >
                                <Trash2 size={16} />
                            </button>
                        </div>
                    </div>
                    <div className="flex justify-between items-center">
                        {city.latitude && city.longitude && (
                            <div className="text-xs text-gray-400">
                                {city.latitude.toFixed(4)}, {city.longitude.toFixed(4)}
                            </div>
                        )}
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            city.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                        }`}>
                            {city.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </div>
                </div>
            ))}
        </div>
    </>
);

// Pagination Component
const Pagination = ({ currentPage, totalPages, onPageChange }) => {
    if (totalPages <= 1) return null;

    return (
        <div className="mt-6 flex justify-center items-center gap-2">
            <button
                onClick={() => onPageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
                <ChevronLeft size={20} />
            </button>

            <div className="flex gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                        pageNum = i + 1;
                    } else if (currentPage <= 3) {
                        pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                    } else {
                        pageNum = currentPage - 2 + i;
                    }

                    return (
                        <button
                            key={pageNum}
                            onClick={() => onPageChange(pageNum)}
                            className={`px-3 py-2 rounded-lg ${
                                currentPage === pageNum
                                    ? 'bg-blue-600 text-white'
                                    : 'border border-gray-300 hover:bg-gray-50'
                            }`}
                        >
                            {pageNum}
                        </button>
                    );
                })}
            </div>

            <button
                onClick={() => onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="p-2 rounded-lg border border-gray-300 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
                <ChevronRight size={20} />
            </button>
        </div>
    );
};

// Empty State Component
const EmptyState = ({ type, searchTerm, onAdd }) => (
    <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
            {type === 'countries' ? <Globe size={48} className="mx-auto" /> : <MapPin size={48} className="mx-auto" />}
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
            No {type} found
        </h3>
        <p className="text-gray-500 mb-4">
            {searchTerm ? 'Try adjusting your search terms' : `Get started by creating your first ${type.slice(0, -1)}`}
        </p>
        {!searchTerm && (
            <button
                onClick={onAdd}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
                Create {type === 'countries' ? 'Country' : 'City'}
            </button>
        )}
    </div>
);

// Modal Component
const Modal = ({ type, editingItem, formData, countries, onInputChange, onSubmit, onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
                <h2 className="text-xl font-bold mb-4">
                    {editingItem ? 'Edit' : 'Create'} {type === 'country' ? 'Country' : 'City'}
                </h2>

                <form onSubmit={onSubmit} className="space-y-4">
                    {type === 'country' ? (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Country Name *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name || ''}
                                        onChange={(e) => onInputChange('name', e.target.value)}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="Enter country name"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Country Code *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.code || ''}
                                        onChange={(e) => onInputChange('code', e.target.value.toUpperCase())}
                                        required
                                        maxLength={2}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="US"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Dial Code *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.dialCode || ''}
                                        onChange={(e) => onInputChange('dialCode', e.target.value)}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="+1"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Flag Emoji
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.flag || ''}
                                        onChange={(e) => onInputChange('flag', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="🇺🇸"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Currency Code *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.currencyCode || ''}
                                        onChange={(e) => onInputChange('currencyCode', e.target.value.toUpperCase())}
                                        required
                                        maxLength={3}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="USD"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Currency Name *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.currencyName || ''}
                                        onChange={(e) => onInputChange('currencyName', e.target.value)}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="US Dollar"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Currency Symbol *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.currencySymbol || ''}
                                        onChange={(e) => onInputChange('currencySymbol', e.target.value)}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="$"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Sort Order
                                    </label>
                                    <input
                                        type="number"
                                        value={formData.sortOrder || 0}
                                        onChange={(e) => onInputChange('sortOrder', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="0"
                                    />
                                </div>
                                <div className="flex items-center pt-6">
                                    <input
                                        type="checkbox"
                                        id="isActive"
                                        checked={formData.isActive || false}
                                        onChange={(e) => onInputChange('isActive', e.target.checked)}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        City Name *
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.name || ''}
                                        onChange={(e) => onInputChange('name', e.target.value)}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="Enter city name"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Country *
                                    </label>
                                    <select
                                        value={formData.country || ''}
                                        onChange={(e) => onInputChange('country', e.target.value)}
                                        required
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    >
                                        <option value="">Select a country</option>
                                        {countries.map((country) => (
                                            <option key={country._id} value={country._id}>
                                                {country.flag} {country.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        State/Province
                                    </label>
                                    <input
                                        type="text"
                                        value={formData.state || ''}
                                        onChange={(e) => onInputChange('state', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="Enter state or province"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Population
                                    </label>
                                    <input
                                        type="number"
                                        value={formData.population || ''}
                                        onChange={(e) => onInputChange('population', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="0"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Latitude
                                    </label>
                                    <input
                                        type="number"
                                        step="any"
                                        value={formData.latitude || ''}
                                        onChange={(e) => onInputChange('latitude', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="0.0000"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Longitude
                                    </label>
                                    <input
                                        type="number"
                                        step="any"
                                        value={formData.longitude || ''}
                                        onChange={(e) => onInputChange('longitude', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="0.0000"
                                    />
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                        Sort Order
                                    </label>
                                    <input
                                        type="number"
                                        value={formData.sortOrder || 0}
                                        onChange={(e) => onInputChange('sortOrder', e.target.value)}
                                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="0"
                                    />
                                </div>
                                <div className="flex items-center pt-6">
                                    <input
                                        type="checkbox"
                                        id="isCapital"
                                        checked={formData.isCapital || false}
                                        onChange={(e) => onInputChange('isCapital', e.target.checked)}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="isCapital" className="ml-2 block text-sm text-gray-900">
                                        Capital City
                                    </label>
                                </div>
                                <div className="flex items-center pt-6">
                                    <input
                                        type="checkbox"
                                        id="isActiveCity"
                                        checked={formData.isActive || false}
                                        onChange={(e) => onInputChange('isActive', e.target.checked)}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label htmlFor="isActiveCity" className="ml-2 block text-sm text-gray-900">
                                        Active
                                    </label>
                                </div>
                            </div>
                        </>
                    )}

                    <div className="flex gap-3 pt-4">
                        <button
                            type="submit"
                            className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg"
                        >
                            {editingItem ? 'Update' : 'Create'} {type === 'country' ? 'Country' : 'City'}
                        </button>
                        <button
                            type="button"
                            onClick={onClose}
                            className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-lg"
                        >
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
);

export default LocationManagement;
