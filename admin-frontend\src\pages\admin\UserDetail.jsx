import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';
import usePageTitle from '../../hooks/usePageTitle';
import { getUserImageData } from '../../utils/imageUtils';

const UserDetail = () => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAdminAuth();
  
  usePageTitle('Admin | User Details');
  
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');

  useEffect(() => {
    fetchUserDetails();
  }, [userId]);

  const fetchUserDetails = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getUserById(userId);
      if (response.success) {
        setUser(response.data.user);
      } else {
        setError('Failed to load user details');
      }
    } catch (error) {
      console.error('Fetch user details error:', error);
      setError('Failed to load user details');
    } finally {
      setLoading(false);
    }
  };

  const handleUserAction = async (action, reason = '') => {
    try {
      let response;
      switch (action) {
        case 'suspend':
          response = await adminApi.suspendUser(userId, reason);
          break;
        case 'reactivate':
          response = await adminApi.reactivateUser(userId);
          break;
        case 'delete':
          response = await adminApi.deleteUser(userId);
          break;
        default:
          return;
      }

      if (response.success) {
        if (action === 'delete') {
          navigate('/admin/users');
        } else {
          fetchUserDetails(); // Refresh user details
        }
        setShowActionModal(false);
        setActionReason('');
      } else {
        setError(response.message || 'Action failed');
      }
    } catch (error) {
      console.error('User action error:', error);
      setError('Action failed');
    }
  };

  const openActionModal = (action) => {
    setActionType(action);
    setShowActionModal(true);
  };

  const confirmAction = () => {
    handleUserAction(actionType, actionReason);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (isActive) => {
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
        isActive 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800'
      }`}>
        {isActive ? 'Active' : 'Suspended'}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error || 'User not found'}
        </div>
        <Link to="/admin/users" className="mt-4 inline-block text-teal-600 hover:text-teal-800">
          ← Back to Users
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <Link to="/admin/users" className="text-teal-600 hover:text-teal-800 mb-2 inline-block">
              ← Back to Users
            </Link>
            <h1 className="text-2xl font-bold text-gray-900">User Details</h1>
          </div>
          <div className="flex space-x-3">
            {hasPermission('users', 'suspend') && user.stats?.isActive && (
              <button
                onClick={() => openActionModal('suspend')}
                className="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700"
              >
                Suspend User
              </button>
            )}
            {hasPermission('users', 'suspend') && !user.stats?.isActive && (
              <button
                onClick={() => handleUserAction('reactivate')}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
              >
                Reactivate User
              </button>
            )}
            {hasPermission('users', 'delete') && (
              <button
                onClick={() => openActionModal('delete')}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Delete User
              </button>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* User Profile */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center">
              {(() => {
                const { imageUrl, initials, hasImage } = getUserImageData(user);
                return (
                  <>
                    {hasImage && (
                      <img
                        className="mx-auto h-24 w-24 rounded-full object-cover"
                        src={imageUrl}
                        alt={`${user.firstName} ${user.lastName}`}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    )}
                    <div
                      className={`mx-auto h-24 w-24 rounded-full bg-gray-300 flex items-center justify-center ${hasImage ? 'hidden' : ''}`}
                    >
                      <span className="text-xl font-medium text-gray-700">
                        {initials}
                      </span>
                    </div>
                  </>
                );
              })()}
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                {user.firstName} {user.lastName}
              </h3>
              <p className="text-sm text-gray-500">@{user.userName}</p>
              <div className="mt-2">
                {getStatusBadge(user.stats?.isActive)}
              </div>
            </div>

            <div className="mt-6 border-t border-gray-200 pt-6">
              <dl className="space-y-3">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="text-sm text-gray-900">{user.email}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Phone</dt>
                  <dd className="text-sm text-gray-900">{user.phone || 'Not provided'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Country</dt>
                  <dd className="text-sm text-gray-900">{user.country}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">City</dt>
                  <dd className="text-sm text-gray-900">{user.city}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Gender</dt>
                  <dd className="text-sm text-gray-900 capitalize">{user.gender}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Joined</dt>
                  <dd className="text-sm text-gray-900">{formatDate(user.createdAt)}</dd>
                </div>
                {user.stats?.joinedDaysAgo && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Days Active</dt>
                    <dd className="text-sm text-gray-900">{user.stats.joinedDaysAgo} days</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>
        </div>

        {/* User Statistics and Activity */}
        <div className="lg:col-span-2 space-y-6">
          {/* Statistics */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Statistics</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{user.stats?.totalProducts || 0}</div>
                <div className="text-sm text-blue-600">Total Products</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{user.stats?.totalOrders || 0}</div>
                <div className="text-sm text-green-600">Total Orders</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-purple-600">${user.stats?.totalSales?.toLocaleString() || 0}</div>
                <div className="text-sm text-purple-600">Total Sales</div>
              </div>
            </div>
          </div>

          {/* Recent Products */}
          {user.recentProducts && user.recentProducts.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Products</h3>
              <div className="space-y-3">
                {user.recentProducts.map((product) => (
                  <div key={product._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{product.title}</div>
                      <div className="text-sm text-gray-500">
                        Status: <span className="capitalize">{product.status}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-gray-900">${product.price}</div>
                      <div className="text-sm text-gray-500">{formatDate(product.createdAt)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Recent Orders */}
          {user.recentOrders && user.recentOrders.length > 0 && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Orders</h3>
              <div className="space-y-3">
                {user.recentOrders.map((order) => (
                  <div key={order._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{order.orderId}</div>
                      <div className="text-sm text-gray-500">
                        {order.productId?.title || 'Product not found'}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-gray-900">${order.totalAmount}</div>
                      <div className="text-sm text-gray-500">
                        <span className="capitalize">{order.status}</span> • {formatDate(order.createdAt)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* About */}
          {user.about && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">About</h3>
              <p className="text-gray-700">{user.about}</p>
            </div>
          )}
        </div>
      </div>

      {/* Action Modal */}
      {showActionModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Confirm {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Are you sure you want to {actionType} this user?
              </p>
              
              {(actionType === 'suspend' || actionType === 'delete') && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reason {actionType === 'suspend' ? '(optional)' : '(required)'}
                  </label>
                  <textarea
                    value={actionReason}
                    onChange={(e) => setActionReason(e.target.value)}
                    placeholder={`Enter reason for ${actionType}...`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                    rows="3"
                  />
                </div>
              )}
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowActionModal(false);
                    setActionReason('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmAction}
                  disabled={actionType === 'delete' && !actionReason}
                  className={`px-4 py-2 rounded-md text-white disabled:opacity-50 ${
                    actionType === 'delete' 
                      ? 'bg-red-600 hover:bg-red-700' 
                      : actionType === 'suspend'
                      ? 'bg-yellow-600 hover:bg-yellow-700'
                      : 'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDetail;
