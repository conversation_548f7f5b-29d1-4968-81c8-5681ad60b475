import React, { useState, useEffect } from 'react';
import MenuData from '../data/menus.json';

const Header = () => {
  const [menuData, setMenuData] = useState([]);
  const [activeDropdown, setActiveDropdown] = useState(null);

  useEffect(() => {
    const savedMenus = localStorage.getItem('menus');
    if (savedMenus) {
      setMenuData(JSON.parse(savedMenus));
    } else {
      setMenuData(MenuData);
    }
  }, []);

  // Group menus by category
  const groupedMenus = menuData.reduce((acc, menu) => {
    if (!acc[menu.category]) {
      acc[menu.category] = [];
    }
    acc[menu.category].push(menu);
    return acc;
  }, {});

  const toggleDropdown = (category) => {
    setActiveDropdown(activeDropdown === category ? null : category);
  };

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <nav className="container mx-auto px-4">
        <div className="flex justify-center">
          <ul className="flex space-x-8">
            {Object.entries(groupedMenus).map(([category, menus]) => (
              <li key={category} className="relative">
                <button
                  onClick={() => toggleDropdown(category)}
                  className="py-4 px-2 text-gray-700 hover:text-blue-600 flex items-center"
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                  <svg
                    className={`ml-1 h-4 w-4 transition-transform ${
                      activeDropdown === category ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>
                {activeDropdown === category && (
                  <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    {menus.map(menu => (
                      <a
                        key={menu.id}
                        href="#"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        {menu.name}
                      </a>
                    ))}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </nav>
    </header>
  );
};

export default Header; 