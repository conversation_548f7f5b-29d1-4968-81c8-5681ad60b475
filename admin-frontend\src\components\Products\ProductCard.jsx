import React from 'react';
import { Heart } from 'lucide-react';
import { getProductImageUrl } from '../../utils/imageUtils';

const ProductCard = ({ product }) => {
  return (
    <div className="group rounded-lg overflow-hidden bg-white border border-gray-200 hover:shadow-md transition-shadow duration-300">
      <div className="relative pb-[125%] overflow-hidden">
        {(() => {
          const imageUrl = getProductImageUrl(product.product_photos) || product.imageUrl;
          return imageUrl ? (
            <img
              src={imageUrl}
              alt={product.title}
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
          ) : null;
        })()}
        <div
          className={`absolute inset-0 bg-gray-200 flex items-center justify-center ${getProductImageUrl(product.product_photos) || product.imageUrl ? 'hidden' : ''}`}
        >
          <svg className="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z" />
          </svg>
        </div>
        <button 
          className="absolute top-3 right-3 w-8 h-8 rounded-full bg-white flex items-center justify-center shadow-sm text-gray-500 hover:text-red-500 transition-colors duration-200"
        >
          <Heart size={18} />
        </button>
      </div>
      <div className="p-3">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-medium text-gray-800 line-clamp-1">{product.title}</h3>
            <div className="mt-1 flex items-center space-x-1">
              <span className="text-xs text-gray-500">{product.size}</span>
              <span className="text-xs text-gray-300">•</span>
              <span className="text-xs text-gray-500">{product.brand}</span>
            </div>
          </div>
          <span className="font-semibold text-gray-900">${product.price.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
