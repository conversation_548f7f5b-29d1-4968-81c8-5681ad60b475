import React from 'react';

const ResponsiveStatsCard = ({ 
  title, 
  value, 
  change, 
  icon, 
  className = '',
  valueColor = 'text-gray-900',
  changeColor = 'text-green-600'
}) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 truncate">{title}</p>
          <p className={`text-2xl sm:text-3xl font-bold ${valueColor} mt-1`}>
            {value}
          </p>
          {change && (
            <p className={`text-xs sm:text-sm ${changeColor} mt-1`}>
              {change}
            </p>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-4">
            <div className="w-8 h-8 sm:w-10 sm:h-10">
              {icon}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponsiveStatsCard;
