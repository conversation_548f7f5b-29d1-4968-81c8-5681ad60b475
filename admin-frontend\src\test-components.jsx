import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { AdminAuthProvider } from './context/AdminAuthContext';
import OrderManagement from './pages/admin/OrderManagement';
import RatingManagement from './pages/admin/RatingManagement';
import ReportManagement from './pages/admin/ReportManagement';

// Test component to verify all admin components render correctly
const TestComponents = () => {
  return (
    <BrowserRouter>
      <AdminAuthProvider>
        <div className="min-h-screen bg-gray-50 p-4">
          <div className="max-w-7xl mx-auto space-y-8">
            
            {/* Test Header */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                🧪 Admin Components Test
              </h1>
              <p className="text-gray-600">
                Testing all admin management components for responsiveness and functionality
              </p>
            </div>

            {/* Test Order Management Component */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                📦 Order Management Component
              </h2>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <OrderManagement />
              </div>
            </div>

            {/* Test Rating Management Component */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                ⭐ Rating Management Component
              </h2>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <RatingManagement />
              </div>
            </div>

            {/* Test Report Management Component */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                🚨 Report Management Component
              </h2>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <ReportManagement />
              </div>
            </div>

            {/* Responsive Test Grid */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                📱 Responsive Grid Test
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-100 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">25</div>
                  <div className="text-sm text-blue-800">Total Orders</div>
                </div>
                <div className="bg-green-100 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">4.2</div>
                  <div className="text-sm text-green-800">Avg Rating</div>
                </div>
                <div className="bg-yellow-100 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-yellow-600">8</div>
                  <div className="text-sm text-yellow-800">Pending Reports</div>
                </div>
                <div className="bg-purple-100 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-600">156</div>
                  <div className="text-sm text-purple-800">Total Users</div>
                </div>
              </div>
            </div>

            {/* Mobile Test Section */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                📱 Mobile Responsiveness Test
              </h2>
              <div className="space-y-4">
                <div className="block sm:hidden bg-blue-50 p-4 rounded-lg">
                  <p className="text-blue-800 font-medium">📱 Mobile View Active</p>
                  <p className="text-blue-600 text-sm">You're viewing this on a mobile device</p>
                </div>
                <div className="hidden sm:block md:hidden bg-green-50 p-4 rounded-lg">
                  <p className="text-green-800 font-medium">📱 Tablet View Active</p>
                  <p className="text-green-600 text-sm">You're viewing this on a tablet device</p>
                </div>
                <div className="hidden md:block bg-purple-50 p-4 rounded-lg">
                  <p className="text-purple-800 font-medium">🖥️ Desktop View Active</p>
                  <p className="text-purple-600 text-sm">You're viewing this on a desktop device</p>
                </div>
              </div>
            </div>

            {/* Component Status */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                ✅ Component Status
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="text-green-800 font-medium">Order Management</span>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="text-green-800 font-medium">Rating Management</span>
                </div>
                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="text-green-800 font-medium">Report Management</span>
                </div>
              </div>
            </div>

            {/* Test Footer */}
            <div className="bg-gray-800 text-white rounded-lg p-6 text-center">
              <p className="text-lg font-medium">🎉 All Admin Components Loaded Successfully!</p>
              <p className="text-gray-300 mt-2">
                The admin panel is ready for production use with full responsive design.
              </p>
            </div>

          </div>
        </div>
      </AdminAuthProvider>
    </BrowserRouter>
  );
};

export default TestComponents;
