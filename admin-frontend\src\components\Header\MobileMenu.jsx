import React from 'react';
import { Search } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { categories } from '../../data/categories';
import { useAppContext } from '../../context/AppContext';

const MobileMenu = () => {
  const navigate = useNavigate();
  const { setIsAuthModalOpen, setAuthMode } = useAppContext();

  const handleLogin = () => {
    navigate('/admin/login');
  };

  const handleSignup = () => {
    navigate('/admin/signup');
  };

  return (
    <div className="md:hidden bg-white border-t border-gray-100 absolute left-0 right-0">
      <div className="p-4">
        <div className="mb-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Search products"
              className="pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-teal-500 w-full"
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>
        </div>

        <div className="space-y-4 mb-6">
          <button
            onClick={handleLogin}
            className="text-gray-700 hover:text-teal-600 font-medium block w-full text-left"
          >
            Login
          </button>
          <button
            onClick={handleSignup}
            className="text-gray-700 hover:text-teal-600 font-medium block w-full text-left"
          >
            Sign up
          </button>
          <button className="bg-teal-500 hover:bg-teal-600 text-white font-medium py-2 px-4 rounded-full transition-colors duration-200 w-full">
            Sell now
          </button>
        </div>

        <div className="space-y-4">
          <h3 className="font-semibold text-gray-800">Categories</h3>
          <ul className="space-y-3">
            {categories.map((category) => (
              <li key={category.id}>
                <button className="text-gray-700 hover:text-teal-600 font-medium">
                  {category.name}
                </button>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
