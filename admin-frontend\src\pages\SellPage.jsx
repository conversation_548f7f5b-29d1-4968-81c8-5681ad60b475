function SellPage() {
  return (
    <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-2xl font-bold mb-8">Sell your items</h1>
      
      <form className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Photos
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <div className="space-y-2">
              <div className="text-gray-600">
                <span className="text-green-500">Upload photos</span> or drag and drop
              </div>
              <p className="text-xs text-gray-500">Up to 20 photos</p>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Title
          </label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            placeholder="e.g., Nike Air Max 90"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            rows="4"
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            placeholder="Describe your item..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Price
          </label>
          <input
            type="number"
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            placeholder="0.00"
          />
        </div>

        <div className="flex items-center">
          <input type="checkbox" className="h-4 w-4 text-green-500" id="terms" />
          <label htmlFor="terms" className="ml-2 text-sm text-gray-600">
            I confirm this is an authentic item and I have the right to sell it
          </label>
        </div>

        <button
          type="submit"
          className="w-full bg-green-500 text-white py-3 rounded-full hover:bg-green-600"
        >
          List Item
        </button>
      </form>
    </div>
  );
}

export default SellPage;