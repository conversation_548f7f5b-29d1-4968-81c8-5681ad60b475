import { useNetworkError as useNetworkErrorContext } from '../context/NetworkErrorContext';

/**
 * Custom hook for handling network errors in admin panel
 * Provides easy-to-use methods for showing network error modals
 */
export const useNetworkError = () => {
  const context = useNetworkErrorContext();

  /**
   * Show a network error modal with custom options
   * @param {Object} options - Configuration options
   * @param {string} options.title - Modal title
   * @param {string} options.message - Error message
   * @param {Function} options.retryFunction - Function to call on retry
   * @param {boolean} options.showRetryButton - Whether to show retry button
   * @param {boolean} options.autoRetry - Whether to auto-retry
   * @param {number} options.maxRetries - Maximum retry attempts
   */
  const showError = (options = {}) => {
    context.showNetworkError(options);
  };

  /**
   * Automatically handle API errors and show appropriate network error modal
   * @param {Error} error - The error object from API call
   * @param {Function} retryFunction - Function to call when user clicks retry
   * @returns {boolean} - True if error was handled, false if not a network error
   */
  const handleApiError = (error, retryFunction = null) => {
    return context.handleApiError(error, retryFunction);
  };

  /**
   * Show a connection error
   * @param {Function} retryFunction - Function to call on retry
   */
  const showConnectionError = (retryFunction = null) => {
    showError({
      title: "Connection Failed",
      message: "Unable to connect to the admin server. Please check your internet connection and try again.",
      retryFunction,
      showRetryButton: !!retryFunction
    });
  };

  /**
   * Show a server error
   * @param {Function} retryFunction - Function to call on retry
   */
  const showServerError = (retryFunction = null) => {
    showError({
      title: "Server Error",
      message: "The admin server is currently unavailable. Please try again later.",
      retryFunction,
      showRetryButton: !!retryFunction
    });
  };

  /**
   * Show a timeout error
   * @param {Function} retryFunction - Function to call on retry
   */
  const showTimeoutError = (retryFunction = null) => {
    showError({
      title: "Connection Timeout",
      message: "The request timed out. Please check your connection and try again.",
      retryFunction,
      showRetryButton: !!retryFunction
    });
  };

  /**
   * Show an offline error
   */
  const showOfflineError = () => {
    showError({
      title: "No Internet Connection",
      message: "You are currently offline. Please check your internet connection.",
      showRetryButton: false
    });
  };

  /**
   * Show backend server down error
   * @param {Function} retryFunction - Function to call on retry
   */
  const showBackendDownError = (retryFunction = null) => {
    showError({
      title: "Backend Server Unavailable",
      message: "The backend server is currently down or unreachable. Please contact your system administrator or try again later.",
      retryFunction,
      showRetryButton: !!retryFunction
    });
  };

  /**
   * Show admin server down error
   * @param {Function} retryFunction - Function to call on retry
   */
  const showAdminServerDownError = (retryFunction = null) => {
    showError({
      title: "Admin Server Unavailable",
      message: "The admin server is currently down or unreachable. Please check if the server is running and try again.",
      retryFunction,
      showRetryButton: !!retryFunction
    });
  };

  /**
   * Hide the network error modal
   */
  const hideError = () => {
    context.hideNetworkError();
  };

  return {
    // State
    isOnline: context.isOnline,
    isModalOpen: context.isModalOpen,
    
    // Methods
    showError,
    hideError,
    handleApiError,
    
    // Convenience methods
    showConnectionError,
    showServerError,
    showTimeoutError,
    showOfflineError,
    showBackendDownError,
    showAdminServerDownError
  };
};

export default useNetworkError;
