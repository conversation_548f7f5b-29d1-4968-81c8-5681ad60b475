/**
 * Responsive utility functions and breakpoint constants for admin panel
 */

// Tailwind CSS breakpoints
export const BREAKPOINTS = {
  sm: '640px',   // Small devices (landscape phones, 640px and up)
  md: '768px',   // Medium devices (tablets, 768px and up)
  lg: '1024px',  // Large devices (desktops, 1024px and up)
  xl: '1280px',  // Extra large devices (large desktops, 1280px and up)
  '2xl': '1536px' // 2X Large devices (larger desktops, 1536px and up)
};

// Common responsive class combinations
export const RESPONSIVE_CLASSES = {
  // Container classes
  container: 'w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  containerSmall: 'w-full max-w-4xl mx-auto px-4 sm:px-6 lg:px-8',
  
  // Grid layouts
  gridCols1: 'grid grid-cols-1',
  gridCols2: 'grid grid-cols-1 md:grid-cols-2',
  gridCols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  gridCols4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
  gridCols6: 'grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6',
  
  // Flex layouts
  flexCol: 'flex flex-col',
  flexRow: 'flex flex-col sm:flex-row',
  flexRowReverse: 'flex flex-col-reverse sm:flex-row',
  
  // Spacing
  gap2: 'gap-2 sm:gap-4',
  gap4: 'gap-4 sm:gap-6',
  gap6: 'gap-6 sm:gap-8',
  
  // Padding
  p4: 'p-4 sm:p-6',
  p6: 'p-6 sm:p-8',
  px4: 'px-4 sm:px-6',
  py4: 'py-4 sm:py-6',
  
  // Text sizes
  textSm: 'text-sm sm:text-base',
  textBase: 'text-base sm:text-lg',
  textLg: 'text-lg sm:text-xl',
  textXl: 'text-xl sm:text-2xl',
  text2xl: 'text-2xl sm:text-3xl',
  
  // Buttons
  btnSm: 'px-3 py-1.5 text-sm sm:px-4 sm:py-2 sm:text-base',
  btnMd: 'px-4 py-2 text-sm sm:px-6 sm:py-3 sm:text-base',
  btnLg: 'px-6 py-3 text-base sm:px-8 sm:py-4 sm:text-lg',
  
  // Cards
  card: 'bg-white rounded-lg shadow-sm border p-4 sm:p-6',
  cardLarge: 'bg-white rounded-lg shadow-sm border p-6 sm:p-8',
  
  // Tables
  tableContainer: 'overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg',
  tableResponsive: 'min-w-full divide-y divide-gray-200',
  
  // Forms
  formGrid: 'grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6',
  formGridLarge: 'grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 sm:gap-6',
  inputResponsive: 'w-full px-3 py-2 text-sm sm:text-base border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500',
  
  // Navigation
  navHidden: 'hidden sm:block',
  navMobile: 'block sm:hidden',
  
  // Sidebar
  sidebarDesktop: 'hidden lg:flex lg:flex-col lg:w-64',
  sidebarMobile: 'lg:hidden',
  
  // Modal
  modalContainer: 'fixed inset-0 z-50 overflow-y-auto',
  modalBackdrop: 'fixed inset-0 bg-black bg-opacity-50',
  modalContent: 'relative bg-white rounded-lg shadow-xl m-4 sm:m-8 max-w-lg sm:max-w-2xl lg:max-w-4xl mx-auto',
  
  // Stats cards
  statsGrid: 'grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 sm:gap-6',
  statsCard: 'bg-white overflow-hidden shadow rounded-lg p-4 sm:p-6',
};

// Responsive table configurations
export const TABLE_RESPONSIVE = {
  // Hide columns on smaller screens
  hideOnMobile: 'hidden sm:table-cell',
  hideOnTablet: 'hidden md:table-cell',
  hideOnSmall: 'hidden lg:table-cell',
  
  // Show only on specific screens
  showOnMobile: 'table-cell sm:hidden',
  showOnTablet: 'hidden sm:table-cell md:hidden',
  
  // Responsive text in tables
  tableCellText: 'text-sm text-gray-900 truncate max-w-xs sm:max-w-sm lg:max-w-none',
  tableHeaderText: 'text-xs font-medium text-gray-500 uppercase tracking-wider',
};

// Mobile-first responsive utilities
export const MOBILE_FIRST = {
  // Show/hide based on screen size
  showOnMobile: 'block sm:hidden',
  hideOnMobile: 'hidden sm:block',
  showOnTablet: 'hidden sm:block lg:hidden',
  showOnDesktop: 'hidden lg:block',
  
  // Flex direction changes
  stackOnMobile: 'flex flex-col sm:flex-row',
  reverseOnMobile: 'flex flex-col-reverse sm:flex-row',
  
  // Width adjustments
  fullOnMobile: 'w-full sm:w-auto',
  autoOnMobile: 'w-auto sm:w-full',
  
  // Text alignment
  centerOnMobile: 'text-center sm:text-left',
  leftOnMobile: 'text-left sm:text-center',
};

// Utility function to combine responsive classes
export const combineClasses = (...classes) => {
  return classes.filter(Boolean).join(' ');
};

// Utility function to get responsive padding based on screen size
export const getResponsivePadding = (size = 'medium') => {
  const sizes = {
    small: 'p-2 sm:p-4',
    medium: 'p-4 sm:p-6',
    large: 'p-6 sm:p-8',
    xlarge: 'p-8 sm:p-12'
  };
  return sizes[size] || sizes.medium;
};

// Utility function to get responsive grid columns
export const getResponsiveGrid = (cols = 3) => {
  const grids = {
    1: 'grid grid-cols-1',
    2: 'grid grid-cols-1 md:grid-cols-2',
    3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    5: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5',
    6: 'grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6'
  };
  return grids[cols] || grids[3];
};

// Utility function to get responsive text size
export const getResponsiveTextSize = (size = 'base') => {
  const sizes = {
    xs: 'text-xs sm:text-sm',
    sm: 'text-sm sm:text-base',
    base: 'text-base sm:text-lg',
    lg: 'text-lg sm:text-xl',
    xl: 'text-xl sm:text-2xl',
    '2xl': 'text-2xl sm:text-3xl',
    '3xl': 'text-3xl sm:text-4xl'
  };
  return sizes[size] || sizes.base;
};

// Breakpoint detection hook utility
export const useBreakpoint = () => {
  if (typeof window === 'undefined') return 'lg'; // Default for SSR
  
  const width = window.innerWidth;
  if (width < 640) return 'xs';
  if (width < 768) return 'sm';
  if (width < 1024) return 'md';
  if (width < 1280) return 'lg';
  if (width < 1536) return 'xl';
  return '2xl';
};
