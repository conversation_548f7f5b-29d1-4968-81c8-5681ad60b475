import { useState, useEffect } from 'react';

// Breakpoint values (matching Tailwind CSS)
const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
};

/**
 * Custom hook for responsive design
 * Returns current breakpoint and utility functions
 */
export const useResponsive = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768
  });

  const [breakpoint, setBreakpoint] = useState('lg');

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setWindowSize({ width, height });

      // Determine current breakpoint
      if (width < BREAKPOINTS.sm) {
        setBreakpoint('xs');
      } else if (width < BREAKPOINTS.md) {
        setBreakpoint('sm');
      } else if (width < BREAKPOINTS.lg) {
        setBreakpoint('md');
      } else if (width < BREAKPOINTS.xl) {
        setBreakpoint('lg');
      } else if (width < BREAKPOINTS['2xl']) {
        setBreakpoint('xl');
      } else {
        setBreakpoint('2xl');
      }
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Utility functions
  const isMobile = breakpoint === 'xs';
  const isTablet = breakpoint === 'sm' || breakpoint === 'md';
  const isDesktop = breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl';
  const isSmallScreen = breakpoint === 'xs' || breakpoint === 'sm';
  const isLargeScreen = breakpoint === 'xl' || breakpoint === '2xl';

  // Breakpoint checkers
  const isBreakpoint = (bp) => breakpoint === bp;
  const isBreakpointUp = (bp) => {
    const currentIndex = Object.keys(BREAKPOINTS).indexOf(breakpoint);
    const targetIndex = Object.keys(BREAKPOINTS).indexOf(bp);
    return currentIndex >= targetIndex;
  };
  const isBreakpointDown = (bp) => {
    const currentIndex = Object.keys(BREAKPOINTS).indexOf(breakpoint);
    const targetIndex = Object.keys(BREAKPOINTS).indexOf(bp);
    return currentIndex <= targetIndex;
  };

  return {
    windowSize,
    breakpoint,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isLargeScreen,
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    // Specific breakpoint checks
    isXs: breakpoint === 'xs',
    isSm: breakpoint === 'sm',
    isMd: breakpoint === 'md',
    isLg: breakpoint === 'lg',
    isXl: breakpoint === 'xl',
    is2xl: breakpoint === '2xl',
    // Breakpoint up checks
    isSmUp: isBreakpointUp('sm'),
    isMdUp: isBreakpointUp('md'),
    isLgUp: isBreakpointUp('lg'),
    isXlUp: isBreakpointUp('xl'),
    // Breakpoint down checks
    isSmDown: isBreakpointDown('sm'),
    isMdDown: isBreakpointDown('md'),
    isLgDown: isBreakpointDown('lg'),
    isXlDown: isBreakpointDown('xl')
  };
};

/**
 * Hook for media query matching
 */
export const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event) => setMatches(event.matches);
    mediaQuery.addEventListener('change', handler);

    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
};

/**
 * Hook for orientation detection
 */
export const useOrientation = () => {
  const { windowSize } = useResponsive();
  const isPortrait = windowSize.height > windowSize.width;
  const isLandscape = windowSize.width > windowSize.height;

  return {
    isPortrait,
    isLandscape,
    orientation: isPortrait ? 'portrait' : 'landscape'
  };
};

/**
 * Hook for responsive grid columns
 */
export const useResponsiveGrid = (config = {}) => {
  const { breakpoint } = useResponsive();
  
  const defaultConfig = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 5,
    '2xl': 6
  };

  const gridConfig = { ...defaultConfig, ...config };
  
  return gridConfig[breakpoint] || gridConfig.lg;
};

/**
 * Hook for responsive table columns
 */
export const useResponsiveTable = (columns = []) => {
  const { isMobile, isTablet } = useResponsive();

  const visibleColumns = columns.filter(column => {
    if (isMobile && column.hideOnMobile) return false;
    if (isTablet && column.hideOnTablet) return false;
    return true;
  });

  return {
    visibleColumns,
    hiddenColumnsCount: columns.length - visibleColumns.length,
    shouldShowMobileView: isMobile && columns.some(col => col.mobileView)
  };
};

/**
 * Hook for responsive navigation
 */
export const useResponsiveNav = () => {
  const { isLgUp } = useResponsive();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Close mobile menu when switching to desktop
  useEffect(() => {
    if (isLgUp) {
      setIsMobileMenuOpen(false);
    }
  }, [isLgUp]);

  const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);
  const closeMobileMenu = () => setIsMobileMenuOpen(false);

  return {
    showDesktopNav: isLgUp,
    showMobileNav: !isLgUp,
    isMobileMenuOpen,
    toggleMobileMenu,
    closeMobileMenu
  };
};

/**
 * Hook for responsive modal sizing
 */
export const useResponsiveModal = (size = 'medium') => {
  const { breakpoint } = useResponsive();

  const sizeConfig = {
    small: {
      xs: 'max-w-sm',
      sm: 'max-w-md',
      md: 'max-w-lg',
      lg: 'max-w-xl',
      xl: 'max-w-2xl',
      '2xl': 'max-w-3xl'
    },
    medium: {
      xs: 'max-w-md',
      sm: 'max-w-lg',
      md: 'max-w-xl',
      lg: 'max-w-2xl',
      xl: 'max-w-4xl',
      '2xl': 'max-w-5xl'
    },
    large: {
      xs: 'max-w-lg',
      sm: 'max-w-xl',
      md: 'max-w-2xl',
      lg: 'max-w-4xl',
      xl: 'max-w-6xl',
      '2xl': 'max-w-7xl'
    }
  };

  return sizeConfig[size]?.[breakpoint] || sizeConfig.medium.lg;
};

export default useResponsive;
