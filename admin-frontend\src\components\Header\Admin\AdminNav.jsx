import { useState, useEffect, useRef } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../../context/AdminAuthContext';
import { combineClasses } from '../../../utils/responsive';

const AdminNav = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { admin, logout, hasPermission } = useAdminAuth();
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showMobileMenu, setShowMobileMenu] = useState(false);
    const [showMoreMenu, setShowMoreMenu] = useState(false);
    const mobileMenuRef = useRef(null);
    const moreMenuRef = useRef(null);

    const isActive = (path) => {
        if (path === '/admin') {
            return location.pathname === '/admin';
        }
        return location.pathname === path || location.pathname.startsWith(path + '/');
    };

    // Close menus when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            // Check if click is on a Link element (allow navigation)
            if (event.target.closest('a')) {
                return;
            }

            if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
                setShowMobileMenu(false);
            }
            if (moreMenuRef.current && !moreMenuRef.current.contains(event.target)) {
                // Check if the click is inside the dropdown menu
                const dropdownMenu = document.querySelector('[data-dropdown-menu="more"]');
                if (dropdownMenu && dropdownMenu.contains(event.target)) {
                    return;
                }
                setShowMoreMenu(false);
            }
        };

        if (showMobileMenu || showMoreMenu) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showMobileMenu, showMoreMenu]);

    // Close menus on route change
    useEffect(() => {
        setShowMobileMenu(false);
        setShowMoreMenu(false);
    }, [location.pathname]);

    const handleLogout = async () => {
        await logout();
        setShowUserMenu(false);
    };

    // Primary navigation items (always visible on desktop)
    const primaryNavItems = [
        {
            path: '/admin',
            label: 'Dashboard',
            show: true,
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z" />
                </svg>
            )
        },
        {
            path: '/admin/users',
            label: 'Users',
            show: hasPermission('users', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
            )
        },
        {
            path: '/admin/listings',
            label: 'Listings',
            show: hasPermission('listings', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            )
        },
        {
            path: '/admin/analytics',
            label: 'Analytics',
            show: hasPermission('analytics', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
            )
        },
        {
            path: '/admin/notifications',
            label: 'Notifications',
            show: hasPermission('notifications', 'read'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4.868 19.462A17.013 17.013 0 003 12C3 5.373 7.373 1 14 1s11 4.373 11 11-4.373 11-11 11a10.99 10.99 0 01-5.45-1.462L4 22l1.868-2.538z" />
                </svg>
            )
        },
        {
            path: '/admin/orders',
            label: 'Orders',
            show: hasPermission('orders', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
            )
        },
        {
            path: '/admin/categories',
            label: 'Categories',
            show: true,
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
            )
        }
    ];

    // Secondary navigation items (shown in "More" dropdown on desktop)
    const secondaryNavItems = [
        {
            path: '/admin/menus',
            label: 'Menus',
            show: true,
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            )
        },
        {
            path: '/admin/sizes',
            label: 'Sizes',
            show: hasPermission('sizes', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                </svg>
            )
        },
        {
            path: '/admin/locations',
            label: 'Locations',
            show: true,
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
            )
        },
        {
            path: '/admin/shipping',
            label: 'Shipping',
            show: hasPermission('shipping', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
            )
        },
        {
            path: '/admin/ratings',
            label: 'Ratings',
            show: hasPermission('ratings', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
            )
        },
        {
            path: '/admin/reports',
            label: 'Reports',
            show: hasPermission('reports', 'view'),
            icon: (
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            )
        }
    ];

    // All navigation items for mobile menu
    const allNavItems = [...primaryNavItems, ...secondaryNavItems];



    return (
        <nav className="bg-white shadow-lg sticky top-0 z-50 border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8">
                <div className="flex justify-between h-16 relative overflow-visible">
                    <div className="flex items-center min-w-0 flex-1">
                        <div className="flex-shrink-0 flex items-center">
                            <Link to="/admin" className="text-lg sm:text-xl lg:text-2xl font-bold text-teal-600 hover:text-teal-700 transition-colors duration-200">
                                <span className="hidden sm:inline">SOUQ Admin</span>
                                <span className="sm:hidden">SOUQ</span>
                            </Link>
                        </div>

                        {/* Desktop Navigation */}
                        <div className="hidden lg:ml-6 xl:ml-8 lg:flex lg:space-x-0.5 xl:space-x-2 lg:flex-1 lg:justify-start lg:max-w-none overflow-visible">
                            {/* Primary Navigation Items */}
                            {primaryNavItems.filter(item => item.show).map((item) => (
                                <Link
                                    key={item.path}
                                    to={item.path}
                                    className={combineClasses(
                                        'relative inline-flex items-center px-2 xl:px-3 py-2 rounded-lg text-xs lg:text-sm font-medium transition-all duration-200 group whitespace-nowrap',
                                        isActive(item.path)
                                            ? 'bg-teal-50 text-teal-700 shadow-sm'
                                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                    )}
                                >
                                    <span className="hidden xl:inline-flex items-center">
                                        {item.icon}
                                    </span>
                                    <span className="truncate">{item.label}</span>
                                    {isActive(item.path) && (
                                        <div className="absolute inset-x-0 bottom-0 h-0.5 bg-teal-500 rounded-full"></div>
                                    )}
                                </Link>
                            ))}

                            {/* More Dropdown */}
                            <div className="relative" ref={moreMenuRef}>
                                <button
                                    onClick={() => setShowMoreMenu(!showMoreMenu)}
                                    className={combineClasses(
                                        'relative inline-flex items-center px-2 xl:px-3 py-2 rounded-lg text-xs lg:text-sm font-medium transition-all duration-200 group whitespace-nowrap',
                                        secondaryNavItems.some(item => item.show && isActive(item.path))
                                            ? 'bg-teal-50 text-teal-700 shadow-sm'
                                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                                    )}
                                    aria-label="More navigation items"
                                >
                                    <span className="hidden xl:inline-flex items-center">
                                        {/* <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                        </svg> */}
                                    </span>
                                    <span className="truncate">More</span>
                                    <svg className={`ml-1 h-3 w-3 transition-transform duration-200 ${showMoreMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                    {secondaryNavItems.some(item => item.show && isActive(item.path)) && (
                                        <div className="absolute inset-x-0 bottom-0 h-0.5 bg-teal-500 rounded-full"></div>
                                    )}
                                </button>
                            </div>
                        </div>
                    </div>
                    {/* Right side - User Menu and Mobile Menu Button */}
                    <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0 ml-4">
                        {/* Mobile menu button */}
                        <button
                            onClick={() => setShowMobileMenu(!showMobileMenu)}
                            className="lg:hidden inline-flex items-center justify-center p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-teal-500 transition-colors duration-200"
                            aria-label="Toggle mobile menu"
                        >
                            <span className="sr-only">Open main menu</span>
                            {showMobileMenu ? (
                                <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            ) : (
                                <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                                </svg>
                            )}
                        </button>

                        {/* User Menu */}
                        <div className="relative">
                            <button
                                onClick={() => setShowUserMenu(!showUserMenu)}
                                className="flex items-center text-sm rounded-lg p-1 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200"
                                aria-label="User menu"
                            >
                                <div className="h-8 w-8 sm:h-9 sm:w-9 rounded-full bg-gradient-to-r from-teal-500 to-teal-600 flex items-center justify-center shadow-sm">
                                    <span className="text-sm font-semibold text-white">
                                        {admin?.firstName?.[0]?.toUpperCase()}{admin?.lastName?.[0]?.toUpperCase()}
                                    </span>
                                </div>
                                <div className="ml-2 hidden lg:block">
                                    <div className="text-sm font-medium text-gray-700 truncate max-w-24 xl:max-w-40">
                                        {admin?.firstName} {admin?.lastName}
                                    </div>
                                    <div className="text-xs text-gray-500 capitalize truncate max-w-24 xl:max-w-40">
                                        {admin?.role?.replace('_', ' ')}
                                    </div>
                                </div>
                                <svg className={`ml-1 h-4 w-4 text-gray-400 hidden lg:block transition-transform duration-200 ${showUserMenu ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            {showUserMenu && (
                                <div className="absolute right-0 mt-2 w-56 sm:w-64 bg-white rounded-lg shadow-xl py-2 z-[60] border border-gray-200 transform origin-top-right">
                                    <div className="px-4 py-3 border-b border-gray-100">
                                        <div className="flex items-center">
                                            <div className="h-10 w-10 rounded-full bg-gradient-to-r from-teal-500 to-teal-600 flex items-center justify-center">
                                                <span className="text-sm font-semibold text-white">
                                                    {admin?.firstName?.[0]?.toUpperCase()}{admin?.lastName?.[0]?.toUpperCase()}
                                                </span>
                                            </div>
                                            <div className="ml-3 flex-1 min-w-0">
                                                <div className="font-medium text-gray-900 truncate">{admin?.firstName} {admin?.lastName}</div>
                                                <div className="text-gray-500 truncate text-sm">{admin?.email}</div>
                                                <div className="text-xs text-teal-600 capitalize font-medium">{admin?.role?.replace('_', ' ')}</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="py-1">
                                        <Link
                                            to="/"
                                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200"
                                            onClick={() => setShowUserMenu(false)}
                                        >
                                            <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                            </svg>
                                            View Site
                                        </Link>

                                        <div className="border-t border-gray-100 my-1"></div>

                                        <button
                                            onClick={handleLogout}
                                            className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200"
                                        >
                                            <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                            </svg>
                                            Sign out
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile menu */}
            {showMobileMenu && (
                <div
                    ref={mobileMenuRef}
                    className="lg:hidden border-t border-gray-200 bg-white shadow-lg"
                >
                    <div className="px-2 pt-2 pb-3 space-y-1">
                        {allNavItems.filter(item => item.show).map((item) => (
                            <Link
                                key={item.path}
                                to={item.path}
                                onClick={() => setShowMobileMenu(false)}
                                className={combineClasses(
                                    'flex items-center px-3 py-3 rounded-lg text-base font-medium transition-all duration-200',
                                    isActive(item.path)
                                        ? 'bg-teal-50 text-teal-700 border-l-4 border-teal-500 shadow-sm'
                                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                )}
                            >
                                <span className="flex items-center">
                                    {item.icon}
                                    {item.label}
                                </span>
                                {isActive(item.path) && (
                                    <div className="ml-auto">
                                        <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                                    </div>
                                )}
                            </Link>
                        ))}
                    </div>

                    {/* Mobile User Info */}
                    <div className="border-t border-gray-200 pt-4 pb-3">
                        <div className="flex items-center px-5">
                            <div className="h-10 w-10 rounded-full bg-gradient-to-r from-teal-500 to-teal-600 flex items-center justify-center">
                                <span className="text-sm font-semibold text-white">
                                    {admin?.firstName?.[0]?.toUpperCase()}{admin?.lastName?.[0]?.toUpperCase()}
                                </span>
                            </div>
                            <div className="ml-3 flex-1 min-w-0">
                                <div className="text-base font-medium text-gray-800 truncate">{admin?.firstName} {admin?.lastName}</div>
                                <div className="text-sm text-gray-500 truncate">{admin?.email}</div>
                                <div className="text-xs text-teal-600 capitalize font-medium">{admin?.role?.replace('_', ' ')}</div>
                            </div>
                        </div>
                        <div className="mt-3 px-2 space-y-1">
                            <Link
                                to="/"
                                className="flex items-center px-3 py-2 rounded-lg text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200"
                                onClick={() => setShowMobileMenu(false)}
                            >
                                <svg className="w-5 h-5 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                                View Site
                            </Link>
                            <button
                                onClick={handleLogout}
                                className="flex items-center w-full text-left px-3 py-2 rounded-lg text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors duration-200"
                            >
                                <svg className="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                </svg>
                                Sign out
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* More Dropdown Menu - Positioned outside nav to avoid overflow issues */}
            {showMoreMenu && (
                <div
                    data-dropdown-menu="more"
                    className="fixed bg-white rounded-lg shadow-xl py-2 border border-gray-200 min-w-48"
                    style={{
                        position: 'fixed',
                        top: '64px', // Just below the navbar
                        right: '500px', // Adjust based on user menu width
                        zIndex: 9999,
                        backgroundColor: 'white',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                        minWidth: '192px'
                    }}
                >
                    {secondaryNavItems.filter(item => item.show).length > 0 ? (
                        secondaryNavItems.filter(item => item.show).map((item) => (
                            <button
                                key={item.path}
                                onClick={(e) => {
                                    console.log('Dropdown item clicked:', item.path);
                                    e.preventDefault();
                                    e.stopPropagation();
                                    setShowMoreMenu(false);
                                    navigate(item.path);
                                }}
                                className={combineClasses(
                                    'flex items-center w-full text-left px-4 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer border-none bg-transparent',
                                    isActive(item.path)
                                        ? 'bg-teal-50 text-teal-700 border-r-2 border-teal-500'
                                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                                )}
                            >
                                {item.icon}
                                {item.label}
                            </button>
                        ))
                    ) : (
                        <div className="px-4 py-2 text-sm text-gray-500">
                            No additional items available
                        </div>
                    )}
                </div>
            )}
        </nav>
    );
};

export default AdminNav;