import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import {
  Search,
  AlertTriangle,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  User,
  TrendingUp,
  RefreshCw,
  MessageSquare,
  Shield
} from 'lucide-react';
import ResponsiveCard from '../../components/common/ResponsiveCard';
import ResponsiveGrid from '../../components/common/ResponsiveGrid';
import ResponsiveStatsCard from '../../components/common/ResponsiveStatsCard';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';
import UserAvatar from '../../components/common/UserAvatar';
import {
  ResponsiveTable,
  ResponsiveTableHeader,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell,
  MobileCard,
  MobileCardHeader,
  MobileCardContent,
  MobileCardField,
  MobileCardActions
} from '../../components/common/ResponsiveTable';
import { adminApi } from '../../services/adminApi';

const ReportManagement = () => {
  const [reports, setReports] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    search: '',
    reason: '',
    status: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    dateFrom: '',
    dateTo: ''
  });
  const [pagination, setPagination] = useState({});
  const [selectedReport, setSelectedReport] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [adminNotes, setAdminNotes] = useState('');

  // Load reports
  const loadReports = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getAllReports(filters);

      if (response && response.success) {
        setReports(response.data.reports || []);
        setPagination(response.data.pagination || {});
      } else {
        console.warn('Invalid reports response format:', response);
        setReports([]);
        setPagination({});
      }
    } catch (error) {
      console.error('Error loading reports:', error);
      // Don't show error toast on initial load to prevent spam
      if (reports.length === 0) {
        console.warn('Failed to load reports on initial load');
      } else {
        toast.error('Failed to load reports');
      }
      setReports([]);
      setPagination({});
    } finally {
      setLoading(false);
    }
  };

  // Load report statistics
  const loadStats = async () => {
    try {
      const response = await adminApi.getReportStats();
      if (response && response.success) {
        setStats(response.data || {});
      } else {
        console.warn('Invalid report stats response format:', response);
        setStats({});
      }
    } catch (error) {
      console.error('Error loading report stats:', error);
      setStats({});
    }
  };

  useEffect(() => {
    loadReports();
  }, [filters]);

  useEffect(() => {
    loadStats();
  }, []);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle report action
  const handleReportAction = async (reportId, status, notes = '') => {
    try {
      // Validate reportId
      if (!reportId || reportId === 'undefined' || reportId === 'unknown') {
        toast.error('Invalid report ID. Please refresh the page and try again.');
        return;
      }

      const response = await adminApi.updateReportStatus(reportId, {
        status,
        adminNotes: notes
      });

      if (response.success) {
        toast.success('Report status updated successfully');
        loadReports();
        loadStats();
        setShowActionModal(false);
        setSelectedReport(null);
        setAdminNotes('');
      }
    } catch (error) {
      console.error('Error updating report status:', error);
      toast.error('Failed to update report status');
    }
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    const colors = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'under_review': 'bg-blue-100 text-blue-800',
      'resolved': 'bg-green-100 text-green-800',
      'dismissed': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  // Get report status badge color (alias for consistency)
  const getReportStatusBadgeColor = getStatusBadgeColor;

  // Get reason display text
  const getReasonDisplayText = (reason) => {
    const reasonMap = {
      'spam': 'Spam',
      'harassment': 'Harassment',
      'inappropriate_content': 'Inappropriate Content',
      'fake_profile': 'Fake Profile',
      'scam_fraud': 'Scam/Fraud',
      'hate_speech': 'Hate Speech',
      'violence_threats': 'Violence/Threats',
      'copyright_violation': 'Copyright Violation',
      'other': 'Other'
    };
    return reasonMap[reason] || reason;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Report Management</h1>
          <p className="text-gray-600 mt-1">Manage and review user reports</p>
        </div>
        <button
          onClick={() => {
            loadReports();
            loadStats();
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Statistics */}
      <ResponsiveGrid cols={4} gap="medium">
        <ResponsiveStatsCard
          title="Total Reports"
          value={stats.totalReports || 0}
          icon={<AlertTriangle className="w-full h-full text-red-600" />}
        />
        <ResponsiveStatsCard
          title="Pending Reports"
          value={stats.pendingReports || 0}
          icon={<Clock className="w-full h-full text-yellow-600" />}
        />
        <ResponsiveStatsCard
          title="Resolved Reports"
          value={stats.resolvedReports || 0}
          icon={<CheckCircle className="w-full h-full text-green-600" />}
        />
        <ResponsiveStatsCard
          title="Recent Reports"
          value={stats.recentReports || 0}
          change="Last 30 days"
          icon={<TrendingUp className="w-full h-full text-blue-600" />}
        />
      </ResponsiveGrid>

      {/* Filters and Table */}
      <ResponsiveCard>
        {/* Filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search reports..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Reason Filter */}
            <select
              value={filters.reason}
              onChange={(e) => handleFilterChange('reason', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Reasons</option>
              <option value="spam">Spam</option>
              <option value="harassment">Harassment</option>
              <option value="inappropriate_content">Inappropriate Content</option>
              <option value="fake_profile">Fake Profile</option>
              <option value="scam_fraud">Scam/Fraud</option>
              <option value="hate_speech">Hate Speech</option>
              <option value="violence_threats">Violence/Threats</option>
              <option value="copyright_violation">Copyright Violation</option>
              <option value="other">Other</option>
            </select>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="under_review">Under Review</option>
              <option value="resolved">Resolved</option>
              <option value="dismissed">Dismissed</option>
            </select>

            {/* Date From */}
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />

            {/* Date To */}
            <input
              type="date"
              value={filters.dateTo}
              onChange={(e) => handleFilterChange('dateTo', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Reports Table */}
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner />
            </div>
          ) : reports.length === 0 ? (
            <div className="text-center py-12">
              <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No reports found</h3>
              <p className="mt-1 text-sm text-gray-500">
                No reports match your current filters.
              </p>
            </div>
          ) : (
            <ResponsiveTable>
              <ResponsiveTableHeader>
                <tr>
                  <ResponsiveTableCell header>Reporter</ResponsiveTableCell>
                  <ResponsiveTableCell header>Reported User</ResponsiveTableCell>
                  <ResponsiveTableCell header>Reason</ResponsiveTableCell>
                  <ResponsiveTableCell header>Description</ResponsiveTableCell>
                  <ResponsiveTableCell header>Status</ResponsiveTableCell>
                  <ResponsiveTableCell header>Date</ResponsiveTableCell>
                  <ResponsiveTableCell header>Actions</ResponsiveTableCell>
                </tr>
              </ResponsiveTableHeader>
              <ResponsiveTableBody>
                {reports.map((report) => (
                  <ResponsiveTableRow
                    key={report._id}
                    mobileCard={
                      <MobileCard>
                        <MobileCardHeader>
                          <div>
                            <span className="text-sm font-medium text-gray-900">
                              {getReasonDisplayText(report.reason || 'other')}
                            </span>
                            <div className="text-xs text-gray-500 mt-1">
                              Report #{report._id ? report._id.slice(-6) : 'N/A'}
                            </div>
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getReportStatusBadgeColor(report.status || 'pending')}`}>
                            {report.status || 'pending'}
                          </span>
                        </MobileCardHeader>

                        <MobileCardContent>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">Reporter:</span>
                            <UserAvatar user={report.reporter} size="sm" showName />
                          </div>

                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">Reported User:</span>
                            <UserAvatar user={report.reported} size="sm" showName />
                          </div>

                          {report.description && (
                            <div className="mt-2">
                              <span className="text-sm text-gray-500">Description:</span>
                              <p className="text-sm text-gray-900 mt-1 line-clamp-3">
                                {report.description}
                              </p>
                            </div>
                          )}

                          <MobileCardField
                            label="Date"
                            value={formatDate(report.createdAt)}
                          />
                        </MobileCardContent>

                        <MobileCardActions>
                          <button
                            onClick={() => {
                              setSelectedReport(report);
                              setShowActionModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 p-1"
                            title="Take Action"
                          >
                            <Shield className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => window.open(`/admin/reports/${report._id}`, '_blank')}
                            className="text-green-600 hover:text-green-900 p-1"
                            title="View Details"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                        </MobileCardActions>
                      </MobileCard>
                    }
                  >
                    <ResponsiveTableCell>
                      <UserAvatar user={report.reporter} showName />
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <UserAvatar user={report.reported} showName />
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <span className="text-sm font-medium text-gray-900">
                        {getReasonDisplayText(report.reason || 'other')}
                      </span>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="text-sm text-gray-900 max-w-xs truncate">
                        {report.description || 'No description provided'}
                      </div>
                    </ResponsiveTableCell>
                    <ResponsiveTableCell>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getReportStatusBadgeColor(report.status || 'pending')}`}>
                        {(report.status || 'pending').replace('_', ' ')}
                      </span>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell className="text-sm text-gray-500">
                      {formatDate(report.createdAt)}
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedReport(report);
                            setShowDetailModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                          title="View Details"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        {report.status === 'pending' && (
                          <>
                            <button
                              onClick={() => {
                                setSelectedReport(report);
                                setActionType('resolved');
                                setShowActionModal(true);
                              }}
                              className="text-green-600 hover:text-green-900"
                              title="Resolve Report"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => {
                                setSelectedReport(report);
                                setActionType('dismissed');
                                setShowActionModal(true);
                              }}
                              className="text-red-600 hover:text-red-900"
                              title="Dismiss Report"
                            >
                              <XCircle className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </ResponsiveTableCell>
                  </ResponsiveTableRow>
                ))}
              </ResponsiveTableBody>
            </ResponsiveTable>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </ResponsiveCard>

      {/* Detail Modal */}
      {showDetailModal && selectedReport && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Report Details
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reason</label>
                  <p className="text-sm text-gray-900">{getReasonDisplayText(selectedReport.reason)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <p className="text-sm text-gray-900">{selectedReport.description}</p>
                </div>
                
                {selectedReport.evidence && selectedReport.evidence.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Evidence</label>
                    <div className="space-y-2">
                      {selectedReport.evidence.map((evidence, index) => (
                        <div key={index} className="border p-2 rounded">
                          <p className="text-xs text-gray-500">Type: {evidence.type}</p>
                          <p className="text-sm text-gray-900">{evidence.content}</p>
                          {evidence.description && (
                            <p className="text-xs text-gray-600">{evidence.description}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedReport.adminNotes && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Admin Notes</label>
                    <p className="text-sm text-gray-900">{selectedReport.adminNotes}</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end mt-6">
                <button
                  onClick={() => {
                    setShowDetailModal(false);
                    setSelectedReport(null);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Modal */}
      {showActionModal && selectedReport && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {actionType === 'resolved' ? 'Resolve Report' : 'Dismiss Report'}
              </h3>
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Admin Notes (Optional)
                </label>
                <textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Add notes about your decision..."
                />
              </div>

              <div className="flex gap-2">
                <button
                  onClick={() => handleReportAction(selectedReport?._id, actionType, adminNotes)}
                  className={`px-4 py-2 text-white rounded-lg ${
                    actionType === 'resolved' 
                      ? 'bg-green-600 hover:bg-green-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {actionType === 'resolved' ? 'Resolve' : 'Dismiss'}
                </button>
                <button
                  onClick={() => {
                    setShowActionModal(false);
                    setSelectedReport(null);
                    setAdminNotes('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportManagement;
