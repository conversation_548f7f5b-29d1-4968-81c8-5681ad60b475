import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';
import usePageTitle from '../../hooks/usePageTitle';
import { getProductImageUrl } from '../../utils/imageUtils';

const ListingDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useAdminAuth();
  
  usePageTitle('Admin | Listing Details');

  const [listing, setListing] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchListingDetail();
  }, [id]);

  const fetchListingDetail = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await adminApi.getListingById(id);
      // Debug logging (can be removed in production)
      console.log('Listing loaded:', response.data.listing?.title);
      console.log('Product Photos count:', response.data.listing?.product_photos?.length || 0);
      setListing(response.data.listing);
    } catch (error) {
      console.error('Error fetching listing detail:', error);
      setError('Failed to load listing details');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (newStatus) => {
    if (!window.confirm(`Are you sure you want to ${newStatus} this listing?`)) {
      return;
    }

    try {
      setActionLoading(true);
      await adminApi.updateListingStatus(id, newStatus);
      await fetchListingDetail(); // Refresh the data
      alert(`Listing ${newStatus} successfully!`);
    } catch (error) {
      console.error(`Error ${newStatus} listing:`, error);
      alert(`Failed to ${newStatus} listing`);
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteListing = async () => {
    if (!window.confirm('Are you sure you want to delete this listing? This action cannot be undone.')) {
      return;
    }

    try {
      setActionLoading(true);
      await adminApi.deleteListing(id);
      alert('Listing deleted successfully!');
      navigate('/admin/listings');
    } catch (error) {
      console.error('Error deleting listing:', error);
      alert('Failed to delete listing');
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">Loading listing details...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">{error}</div>
          <div className="mt-4 space-x-4">
            <button 
              onClick={fetchListingDetail}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm"
            >
              Retry
            </button>
            <Link 
              to="/admin/listings"
              className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm"
            >
              Back to Listings
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!listing) {
    return (
      <div className="p-6">
        <div className="text-center">
          <div className="text-lg text-gray-600 mb-4">Listing not found</div>
          <Link 
            to="/admin/listings"
            className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm"
          >
            Back to Listings
          </Link>
        </div>
      </div>
    );
  }

  // Handle different possible image field names
  const productImages = listing.product_photos || listing.photos || listing.images || [];
  const imageUrl = getProductImageUrl(productImages);
console.log('Image URL:', imageUrl);
  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Listing Details</h1>
          <p className="text-gray-600">ID: {listing._id}</p>
        </div>
        <Link
          to="/admin/listings"
          className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
        >
          Back to Listings
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Images */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Product Images</h3>

            {/* Debug Information - Remove in production */}
            {/* {process.env.NODE_ENV === 'development' && (
              <div className="mb-4 p-2 bg-gray-100 rounded text-xs">
                <strong>Debug:</strong>
                <br />Photos Length: {productImages?.length || 0}
                <br />Has Photos: {productImages && productImages.length > 0 ? 'Yes' : 'No'}
                {productImages?.length > 0 && (
                  <div>
                    <br />Sample Image: {productImages[0]}
                  </div>
                )}
              </div>
            )} */}

            <div className="space-y-4">
              {productImages && productImages.length > 0 ? (
                productImages.map((photo, index) => {
                  const imageUrl = photo.startsWith('http') ? photo : `http://localhost:5001${photo}`;
                  console.log(`Image ${index + 1}:`, photo, '→', imageUrl);

                  // return (
                  //   <div key={index} className="relative">
                  //     <img
                  //       src={imageUrl}
                  //       alt={`Product ${index + 1}`}
                  //       className="w-full h-48 object-cover rounded-lg border"
                  //       onError={(e) => {
                  //         console.error(`Image ${index + 1} failed to load:`, imageUrl);
                  //         e.target.style.display = 'none';
                  //         // Show error message instead
                  //         const errorDiv = document.createElement('div');
                  //         errorDiv.className = 'w-full h-48 bg-red-100 rounded-lg flex items-center justify-center text-red-600 text-sm';
                  //         errorDiv.textContent = `Failed to load image`;
                  //         e.target.parentNode.appendChild(errorDiv);
                  //       }}
                  //     />
                  //   </div>
                  // );
                })
              ) : (
                <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500">No images available</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Product Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Product Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Title</label>
                <p className="mt-1 text-sm text-gray-900">{listing.title}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Price</label>
                <p className="mt-1 text-sm text-gray-900">${listing.price}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Brand</label>
                <p className="mt-1 text-sm text-gray-900">{listing.brand || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Size</label>
                <p className="mt-1 text-sm text-gray-900">{listing.size || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Condition</label>
                <p className="mt-1 text-sm text-gray-900">{listing.condition || 'N/A'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Status</label>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  listing.status === 'approved' ? 'bg-green-100 text-green-800' :
                  listing.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {listing.status}
                </span>
              </div>
            </div>
            
            {listing.description && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700">Description</label>
                <p className="mt-1 text-sm text-gray-900">{listing.description}</p>
              </div>
            )}
          </div>

          {/* Seller Information */}
          {listing.user && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Seller Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {listing.user.firstName} {listing.user.lastName}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{listing.user.email}</p>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Actions</h3>
            <div className="flex flex-wrap gap-3">
              {hasPermission('listings', 'approve') && listing.status === 'pending' && (
                <button
                  onClick={() => handleStatusChange('approved')}
                  disabled={actionLoading}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
                >
                  {actionLoading ? 'Processing...' : 'Approve'}
                </button>
              )}
              
              {hasPermission('listings', 'reject') && listing.status === 'pending' && (
                <button
                  onClick={() => handleStatusChange('rejected')}
                  disabled={actionLoading}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
                >
                  {actionLoading ? 'Processing...' : 'Reject'}
                </button>
              )}
              
              {hasPermission('listings', 'delete') && (
                <button
                  onClick={handleDeleteListing}
                  disabled={actionLoading}
                  className="bg-red-800 hover:bg-red-900 text-white px-4 py-2 rounded-md text-sm disabled:opacity-50"
                >
                  {actionLoading ? 'Deleting...' : 'Delete Listing'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListingDetail;
