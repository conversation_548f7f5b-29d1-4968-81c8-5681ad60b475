/* Responsive Admin Panel Styles */

/* Base responsive utilities */
.responsive-container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .responsive-container {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    padding: 0 2rem;
  }
}

/* Navigation responsive styles */
.admin-nav {
  position: sticky;
  top: 0;
  z-index: 40;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.admin-nav-mobile-menu {
  display: none;
}

@media (max-width: 1023px) {
  .admin-nav-desktop {
    display: none;
  }
  
  .admin-nav-mobile-menu {
    display: block;
  }
}

/* Table responsive styles */
.responsive-table-container {
  overflow-x: auto;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
}

.responsive-table {
  min-width: 100%;
  border-collapse: collapse;
}

.responsive-table th,
.responsive-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

@media (min-width: 640px) {
  .responsive-table th,
  .responsive-table td {
    padding: 1.5rem;
  }
}

/* Hide columns on smaller screens */
@media (max-width: 639px) {
  .hide-on-mobile {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .hide-on-tablet {
    display: none !important;
  }
}

@media (max-width: 1023px) {
  .hide-on-small {
    display: none !important;
  }
}

/* Show only on specific screens */
@media (min-width: 640px) {
  .show-on-mobile {
    display: none !important;
  }
}

@media (min-width: 768px) {
  .show-on-tablet {
    display: none !important;
  }
}

/* Card responsive styles */
.responsive-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

@media (min-width: 640px) {
  .responsive-card {
    padding: 1.5rem;
  }
}

/* Button responsive styles */
.responsive-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.responsive-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.5);
}

.responsive-button-small {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.responsive-button-medium {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.responsive-button-large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

@media (min-width: 640px) {
  .responsive-button-small {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
  
  .responsive-button-medium {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
  
  .responsive-button-large {
    padding: 1rem 2rem;
    font-size: 1.125rem;
  }
}

/* Form responsive styles */
.responsive-form-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-form-grid {
    gap: 1.5rem;
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-form-grid-large {
    grid-template-columns: repeat(3, 1fr);
  }
}

.responsive-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.responsive-input:focus {
  outline: none;
  border-color: #14b8a6;
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.1);
}

@media (min-width: 640px) {
  .responsive-input {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
}

/* Grid responsive styles */
.responsive-grid {
  display: grid;
  gap: 1rem;
}

.responsive-grid-1 {
  grid-template-columns: 1fr;
}

.responsive-grid-2 {
  grid-template-columns: 1fr;
}

.responsive-grid-3 {
  grid-template-columns: 1fr;
}

.responsive-grid-4 {
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: 1.5rem;
  }
  
  .responsive-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Modal responsive styles */
.responsive-modal {
  position: fixed;
  inset: 0;
  z-index: 50;
  overflow-y: auto;
}

.responsive-modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
}

.responsive-modal-content {
  position: relative;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  margin: 1rem;
  width: calc(100% - 2rem);
  max-width: 28rem;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .responsive-modal-content {
    margin: 2rem;
    max-width: 42rem;
  }
}

@media (min-width: 1024px) {
  .responsive-modal-content {
    max-width: 56rem;
  }
}

/* Stats card responsive styles */
.responsive-stats-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1rem;
  position: relative;
}

@media (min-width: 640px) {
  .responsive-stats-card {
    padding: 1.5rem;
  }
}

.responsive-stats-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

@media (min-width: 640px) {
  .responsive-stats-value {
    font-size: 1.875rem;
  }
}

/* Text responsive styles */
.responsive-text-sm {
  font-size: 0.875rem;
}

.responsive-text-base {
  font-size: 1rem;
}

.responsive-text-lg {
  font-size: 1.125rem;
}

.responsive-text-xl {
  font-size: 1.25rem;
}

.responsive-text-2xl {
  font-size: 1.5rem;
}

@media (min-width: 640px) {
  .responsive-text-sm {
    font-size: 1rem;
  }
  
  .responsive-text-base {
    font-size: 1.125rem;
  }
  
  .responsive-text-lg {
    font-size: 1.25rem;
  }
  
  .responsive-text-xl {
    font-size: 1.5rem;
  }
  
  .responsive-text-2xl {
    font-size: 1.875rem;
  }
}

/* Utility classes for responsive behavior */
.stack-on-mobile {
  display: flex;
  flex-direction: column;
}

@media (min-width: 640px) {
  .stack-on-mobile {
    flex-direction: row;
  }
}

.center-on-mobile {
  text-align: center;
}

@media (min-width: 640px) {
  .center-on-mobile {
    text-align: left;
  }
}

.full-width-on-mobile {
  width: 100%;
}

@media (min-width: 640px) {
  .full-width-on-mobile {
    width: auto;
  }
}
