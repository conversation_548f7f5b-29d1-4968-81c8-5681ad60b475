import React from 'react';

const ResponsiveTable = ({ 
  children, 
  className = '',
  mobileCardView = true 
}) => {
  return (
    <>
      {/* Desktop Table View */}
      <div className={`hidden lg:block overflow-x-auto ${className}`}>
        <table className="min-w-full divide-y divide-gray-200">
          {children}
        </table>
      </div>
      
      {/* Mobile Card View */}
      {mobileCardView && (
        <div className="lg:hidden space-y-4">
          {children}
        </div>
      )}
    </>
  );
};

const ResponsiveTableHeader = ({ children, className = '' }) => {
  return (
    <thead className={`bg-gray-50 ${className}`}>
      {children}
    </thead>
  );
};

const ResponsiveTableBody = ({ children, className = '' }) => {
  return (
    <tbody className={`bg-white divide-y divide-gray-200 ${className}`}>
      {children}
    </tbody>
  );
};

const ResponsiveTableRow = ({ 
  children, 
  className = '',
  onClick,
  mobileCard = null 
}) => {
  return (
    <>
      {/* Desktop Row */}
      <tr 
        className={`hidden lg:table-row hover:bg-gray-50 ${onClick ? 'cursor-pointer' : ''} ${className}`}
        onClick={onClick}
      >
        {children}
      </tr>
      
      {/* Mobile Card */}
      {mobileCard && (
        <div 
          className={`lg:hidden bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${onClick ? 'cursor-pointer hover:shadow-md' : ''}`}
          onClick={onClick}
        >
          {mobileCard}
        </div>
      )}
    </>
  );
};

const ResponsiveTableCell = ({ 
  children, 
  className = '',
  header = false 
}) => {
  const baseClasses = header 
    ? "px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
    : "px-6 py-4 whitespace-nowrap";
    
  const Tag = header ? 'th' : 'td';
  
  return (
    <Tag className={`${baseClasses} ${className}`}>
      {children}
    </Tag>
  );
};

// Mobile-specific components
const MobileCard = ({ children, className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 ${className}`}>
      {children}
    </div>
  );
};

const MobileCardHeader = ({ children, className = '' }) => {
  return (
    <div className={`flex items-center justify-between mb-3 ${className}`}>
      {children}
    </div>
  );
};

const MobileCardContent = ({ children, className = '' }) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {children}
    </div>
  );
};

const MobileCardField = ({ 
  label, 
  value, 
  className = '',
  labelClassName = '',
  valueClassName = '' 
}) => {
  return (
    <div className={`flex justify-between items-center ${className}`}>
      <span className={`text-sm text-gray-500 ${labelClassName}`}>
        {label}:
      </span>
      <span className={`text-sm text-gray-900 font-medium ${valueClassName}`}>
        {value}
      </span>
    </div>
  );
};

const MobileCardActions = ({ children, className = '' }) => {
  return (
    <div className={`flex items-center justify-end space-x-2 mt-3 pt-3 border-t border-gray-100 ${className}`}>
      {children}
    </div>
  );
};

// Export all components
export {
  ResponsiveTable,
  ResponsiveTableHeader,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell,
  MobileCard,
  MobileCardHeader,
  MobileCardContent,
  MobileCardField,
  MobileCardActions
};

export default ResponsiveTable;
