import React, { useState } from 'react';
import AuthModal from '../components/Auth/AuthModal';
import FilterSidebar from '../components/Filters/FilterSidebar';
import ProductGrid from '../components/Products/ProductGrid';
import { products } from '../data/products';
import HeaderCategories from '../components/Header/HeaderCategories';
import MegaMenu from '../components/Header/MegaMenu';
import usePageTitle from '../hooks/usePageTitle';

const HomePage = () => {
  usePageTitle('SOUQ E-commerce');
  const [activeTab, setActiveTab] = useState('all');

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="bg-gradient-to-r from-teal-400 to-teal-500 rounded-xl overflow-hidden mb-8">
        <div className="flex flex-col md:flex-row items-center">
          <div className="p-8 md:p-12 md:w-1/2">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Turn your closet into cash
            </h1>
            <p className="text-white text-lg mb-6">
              Buy and sell pre-loved fashion. Join millions of people making a difference.
            </p>
            <button className="bg-white text-teal-600 font-medium py-3 px-6 rounded-full hover:bg-gray-100 transition-colors duration-200">
              Sell now
            </button>
          </div>
          <div className="md:w-1/2">
            <img
              src="https://images.pexels.com/photos/5698851/pexels-photo-5698851.jpeg"
              alt="People with clothes"
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>

      {/* Category Tabs */}


      {/* Main Content with Sidebar and Products */}
      <div className="flex flex-col gap-6">
        {/* Products */}
        <div className="flex-grow">
          <div className="mb-6 flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-800">
              {activeTab === 'all' ? 'All Items' : `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}'s Items`}
            </h2>
            <div className="flex items-center">
              <span className="text-sm text-gray-500 mr-2">Sort by:</span>
              <select className="py-1.5 pl-3 pr-8 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal-500">
                <option>Relevance</option>
                <option>Newest</option>
                <option>Price: low to high</option>
                <option>Price: high to low</option>
              </select>
            </div>
          </div>

          <ProductGrid products={products} />

          {/* Load More Button */}
          <div className="mt-8 text-center">
            <button className="py-2.5 px-6 border border-gray-300 rounded-full text-gray-700 hover:bg-gray-50 transition-colors duration-200">
              Load more
            </button>
          </div>
        </div>
      </div>
      <MegaMenu />
      {/* Auth Modal */}
      <AuthModal />
    </div>
  );
};

export default HomePage;
