import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Truck, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  ToggleLeft,
  ToggleRight,
  Settings,
  MapPin,
  Clock,
  DollarSign,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';

const ShippingManagement = () => {
  const { hasPermission } = useAdminAuth();
  const [activeTab, setActiveTab] = useState('providers');
  const [providers, setProviders] = useState([]);
  const [deliveryOptions, setDeliveryOptions] = useState([]);
  const [shipments, setShipments] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // Filters and pagination
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    provider: '',
    page: 1,
    limit: 10
  });
  
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0
  });

  // Modals
  const [showProviderModal, setShowProviderModal] = useState(false);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [showShipmentModal, setShowShipmentModal] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [selectedShipment, setSelectedShipment] = useState(null);

  useEffect(() => {
    loadData();
  }, [activeTab, filters]);

  const loadData = async () => {
    setLoading(true);
    setError('');
    
    try {
      switch (activeTab) {
        case 'providers':
          await loadProviders();
          await loadProviderStats();
          break;
        case 'delivery-options':
          await loadDeliveryOptions();
          await loadDeliveryOptionsStats();
          break;
        case 'shipments':
          await loadShipments();
          await loadShipmentStats();
          break;
        default:
          break;
      }
    } catch (err) {
      setError(err.message || 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadProviders = async () => {
    const params = {
      page: filters.page,
      limit: filters.limit,
      ...(filters.search && { search: filters.search }),
      ...(filters.status && { status: filters.status })
    };

    const data = await adminApi.getShippingProviders(params);
    setProviders(data.data.providers);
    setPagination(data.data.pagination);
  };

  const loadProviderStats = async () => {
    const data = await adminApi.getShippingProviderStats();
    setStats(data.data);
  };

  const loadDeliveryOptions = async () => {
    const params = {
      page: filters.page,
      limit: filters.limit,
      ...(filters.search && { search: filters.search }),
      ...(filters.status && { status: filters.status }),
      ...(filters.provider && { provider: filters.provider })
    };

    const data = await adminApi.getDeliveryOptions(params);
    setDeliveryOptions(data.data.deliveryOptions);
    setPagination(data.data.pagination);
  };

  const loadDeliveryOptionsStats = async () => {
    const data = await adminApi.getDeliveryOptionsStats();
    setStats(data.data);
  };

  const loadShipments = async () => {
    const params = {
      page: filters.page,
      limit: filters.limit,
      ...(filters.search && { search: filters.search }),
      ...(filters.status && { status: filters.status }),
      ...(filters.provider && { provider: filters.provider })
    };

    const data = await adminApi.getShipments(params);
    setShipments(data.data.shipments);
    setPagination(data.data.pagination);
  };

  const loadShipmentStats = async () => {
    const data = await adminApi.getShipmentStats();
    setStats(data.data);
  };

  const toggleProviderStatus = async (providerId) => {
    try {
      await adminApi.toggleShippingProviderStatus(providerId);
      await loadProviders();
    } catch (err) {
      setError(err.message);
    }
  };

  const deleteProvider = async (providerId) => {
    if (!window.confirm('Are you sure you want to delete this provider?')) {
      return;
    }

    try {
      await adminApi.deleteShippingProvider(providerId);
      await loadProviders();
    } catch (err) {
      setError(err.response?.data?.error || err.message);
    }
  };

  const deleteDeliveryOption = async (optionId) => {
    if (!window.confirm('Are you sure you want to delete this delivery option?')) {
      return;
    }

    try {
      await adminApi.deleteDeliveryOption(optionId);
      await loadDeliveryOptions();
    } catch (err) {
      setError(err.message);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const viewShipmentDetails = async (shipmentId) => {
    try {
      const response = await adminApi.getShipmentById(shipmentId);
      if (response.success) {
        setSelectedShipment(response.data.shipment);
        setShowShipmentModal(true);
      }
    } catch (error) {
      setError('Failed to load shipment details');
    }
  };

  const updateShipmentStatus = async (shipmentId, newStatus) => {
    try {
      const response = await adminApi.updateShipmentStatus(shipmentId, { status: newStatus });
      if (response.success) {
        // Reload shipments to reflect the change
        await loadShipments();
        setShowShipmentModal(false);
      }
    } catch (error) {
      setError('Failed to update shipment status');
    }
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in_transit':
        return <Truck className="w-4 h-4 text-blue-500" />;
      case 'exception':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status, isActive = null) => {
    if (isActive !== null) {
      return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          isActive 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {isActive ? 'Active' : 'Inactive'}
        </span>
      );
    }

    const statusColors = {
      delivered: 'bg-green-100 text-green-800',
      in_transit: 'bg-blue-100 text-blue-800',
      picked_up: 'bg-purple-100 text-purple-800',
      out_for_delivery: 'bg-orange-100 text-orange-800',
      exception: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      created: 'bg-yellow-100 text-yellow-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        statusColors[status] || 'bg-gray-100 text-gray-800'
      }`}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status?.replace('_', ' ')}</span>
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Package className="w-8 h-8 text-teal-600 mr-3" />
                Shipping Management
              </h1>
              <p className="mt-2 text-gray-600">
                Manage shipping providers, delivery options, and shipments
              </p>
            </div>
            
            {hasPermission('shipping', 'create') && (
              <div className="mt-4 sm:mt-0 flex space-x-3">
                <button
                  onClick={() => setShowProviderModal(true)}
                  className="inline-flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Provider
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 mr-2" />
              {error}
            </div>
          </div>
        )}

        {/* Statistics Cards */}
        {stats.overview && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="p-2 bg-teal-100 rounded-lg">
                  <Package className="w-6 h-6 text-teal-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    {activeTab === 'providers' ? 'Total Providers' :
                     activeTab === 'delivery-options' ? 'Total Options' : 'Total Shipments'}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {activeTab === 'providers' ? stats.overview.totalProviders :
                     activeTab === 'delivery-options' ? stats.overview.totalOptions :
                     stats.overview.totalShipments}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    {activeTab === 'providers' ? 'Active Providers' :
                     activeTab === 'delivery-options' ? 'Active Options' : 'Delivered'}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {activeTab === 'providers' ? stats.overview.activeProviders :
                     activeTab === 'delivery-options' ? stats.overview.activeOptions :
                     stats.shipmentsByStatus?.find(s => s._id === 'delivered')?.count || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="p-2 bg-red-100 rounded-lg">
                  <XCircle className="w-6 h-6 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    {activeTab === 'providers' ? 'Inactive Providers' :
                     activeTab === 'delivery-options' ? 'Inactive Options' : 'In Transit'}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {activeTab === 'providers' ? stats.overview.inactiveProviders :
                     activeTab === 'delivery-options' ? stats.overview.inactiveOptions :
                     stats.shipmentsByStatus?.find(s => s._id === 'in_transit')?.count || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">
                    {activeTab === 'providers' ? 'Total Shipments' :
                     activeTab === 'delivery-options' ? 'Default Options' : 'Exceptions'}
                  </p>
                  <p className="text-2xl font-semibold text-gray-900">
                    {activeTab === 'providers' ? stats.overview.totalShipments :
                     activeTab === 'delivery-options' ? stats.overview.defaultOptions :
                     stats.shipmentsByStatus?.find(s => s._id === 'exception')?.count || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {[
                { id: 'providers', label: 'Shipping Providers', icon: Truck },
                { id: 'delivery-options', label: 'Delivery Options', icon: MapPin },
                { id: 'shipments', label: 'Shipments', icon: Package }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-teal-500 text-teal-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Filters */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder={`Search ${activeTab.replace('-', ' ')}...`}
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>

                {/* Status Filter */}
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                >
                  <option value="">All Status</option>
                  {activeTab === 'providers' || activeTab === 'delivery-options' ? (
                    <>
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </>
                  ) : (
                    <>
                      <option value="created">Created</option>
                      <option value="picked_up">Picked Up</option>
                      <option value="in_transit">In Transit</option>
                      <option value="out_for_delivery">Out for Delivery</option>
                      <option value="delivered">Delivered</option>
                      <option value="exception">Exception</option>
                      <option value="cancelled">Cancelled</option>
                    </>
                  )}
                </select>

                {/* Provider Filter (for delivery options and shipments) */}
                {(activeTab === 'delivery-options' || activeTab === 'shipments') && (
                  <select
                    value={filters.provider}
                    onChange={(e) => handleFilterChange('provider', e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  >
                    <option value="">All Providers</option>
                    {providers.map((provider) => (
                      <option key={provider._id} value={provider._id}>
                        {provider.displayName}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              {/* Results count */}
              <div className="text-sm text-gray-500">
                Showing {((pagination.currentPage - 1) * filters.limit) + 1} to{' '}
                {Math.min(pagination.currentPage * filters.limit, pagination.totalCount)} of{' '}
                {pagination.totalCount} results
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'providers' && (
              <ProvidersTable
                providers={providers}
                onToggleStatus={toggleProviderStatus}
                onDelete={deleteProvider}
                onEdit={(provider) => {
                  setSelectedProvider(provider);
                  setShowProviderModal(true);
                }}
                hasPermission={hasPermission}
              />
            )}

            {activeTab === 'delivery-options' && (
              <DeliveryOptionsTable
                deliveryOptions={deliveryOptions}
                onDelete={deleteDeliveryOption}
                hasPermission={hasPermission}
              />
            )}

            {activeTab === 'shipments' && (
              <ShipmentsTable
                shipments={shipments}
                hasPermission={hasPermission}
                onViewDetails={viewShipmentDetails}
                onUpdateStatus={updateShipmentStatus}
              />
            )}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={!pagination.hasPrevPage}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`px-3 py-2 text-sm font-medium rounded-lg ${
                            pagination.currentPage === page
                              ? 'bg-teal-600 text-white'
                              : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    })}
                  </div>

                  <button
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                    className="px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>

                <div className="text-sm text-gray-500">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Shipment Detail Modal */}
        <ShipmentDetailModal
          shipment={selectedShipment}
          isOpen={showShipmentModal}
          onClose={() => setShowShipmentModal(false)}
          onUpdateStatus={updateShipmentStatus}
          hasPermission={hasPermission}
        />
      </div>
    </div>
  );
};

// Providers Table Component
const ProvidersTable = ({ providers, onToggleStatus, onDelete, onEdit, hasPermission }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Provider
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Services
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Shipments
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Base Fee
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {providers.map((provider) => (
            <tr key={provider._id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-lg bg-teal-100 flex items-center justify-center">
                      <Truck className="w-5 h-5 text-teal-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">
                      {provider.displayName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {provider.name}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    provider.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {provider.isActive ? 'Active' : 'Inactive'}
                  </span>
                  {hasPermission('shipping', 'update') && (
                    <button
                      onClick={() => onToggleStatus(provider._id)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      {provider.isActive ? (
                        <ToggleRight className="w-5 h-5" />
                      ) : (
                        <ToggleLeft className="w-5 h-5" />
                      )}
                    </button>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {provider.supportedServices?.length || 0} services
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {provider.statistics?.totalShipments || 0}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${provider.pricing?.baseFee || 0} {provider.pricing?.currency || 'USD'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => onEdit(provider)}
                    className="text-teal-600 hover:text-teal-900"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  {hasPermission('shipping', 'update') && (
                    <button
                      onClick={() => onEdit(provider)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                  {hasPermission('shipping', 'delete') && (
                    <button
                      onClick={() => onDelete(provider._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Delivery Options Table Component
const DeliveryOptionsTable = ({ deliveryOptions, onDelete, hasPermission }) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              User
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Provider
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Service
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Default
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {deliveryOptions.map((option) => (
            <tr key={option._id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-8 w-8">
                    <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <Users className="w-4 h-4 text-gray-600" />
                    </div>
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900">
                      {option.user?.firstName} {option.user?.lastName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {option.user?.email}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {option.shippingProvider?.displayName}
                </div>
                <div className="text-sm text-gray-500">
                  {option.shippingProvider?.name}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {option.serviceName}
                </div>
                <div className="text-sm text-gray-500">
                  {option.serviceCode}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  option.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {option.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {option.isDefault && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Default
                  </span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(option.createdAt).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <button className="text-teal-600 hover:text-teal-900">
                    <Eye className="w-4 h-4" />
                  </button>
                  {hasPermission('shipping', 'delete') && (
                    <button
                      onClick={() => onDelete(option._id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Shipments Table Component
const ShipmentsTable = ({ shipments, hasPermission, onViewDetails, onUpdateStatus }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in_transit':
        return <Truck className="w-4 h-4 text-blue-500" />;
      case 'exception':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      delivered: 'bg-green-100 text-green-800',
      in_transit: 'bg-blue-100 text-blue-800',
      picked_up: 'bg-purple-100 text-purple-800',
      out_for_delivery: 'bg-orange-100 text-orange-800',
      exception: 'bg-red-100 text-red-800',
      cancelled: 'bg-gray-100 text-gray-800',
      created: 'bg-yellow-100 text-yellow-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        statusColors[status] || 'bg-gray-100 text-gray-800'
      }`}>
        {getStatusIcon(status)}
        <span className="ml-1 capitalize">{status?.replace('_', ' ')}</span>
      </span>
    );
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Tracking Number
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Order
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Provider
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Created
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Last Update
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {shipments.map((shipment) => (
            <tr key={shipment._id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {shipment.trackingNumber}
                </div>
                <div className="text-sm text-gray-500">
                  {shipment.providerShipmentId}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {shipment.order?.orderNumber}
                </div>
                <div className="text-sm text-gray-500">
                  Status: {shipment.order?.status}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900">
                  {shipment.shippingProvider?.displayName}
                </div>
                <div className="text-sm text-gray-500">
                  {shipment.shippingProvider?.name}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                {getStatusBadge(shipment.tracking?.status)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {new Date(shipment.createdAt).toLocaleDateString()}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {shipment.tracking?.lastUpdate ?
                  new Date(shipment.tracking.lastUpdate).toLocaleDateString() :
                  'N/A'
                }
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex items-center justify-end space-x-2">
                  <button
                    onClick={() => onViewDetails(shipment._id)}
                    className="text-teal-600 hover:text-teal-900"
                    title="View Details"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  {hasPermission('shipping', 'update') && (
                    <button
                      onClick={() => onViewDetails(shipment._id)}
                      className="text-blue-600 hover:text-blue-900"
                      title="Update Status"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Shipment Detail Modal Component
const ShipmentDetailModal = ({ shipment, isOpen, onClose, onUpdateStatus, hasPermission }) => {
  const [newStatus, setNewStatus] = useState('');

  if (!isOpen || !shipment) return null;

  const handleStatusUpdate = () => {
    if (newStatus && newStatus !== shipment.tracking?.status) {
      onUpdateStatus(shipment._id, newStatus);
    }
  };

  const statusOptions = [
    'created', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'returned', 'cancelled'
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Shipment Details - {shipment.trackingNumber}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XCircle className="w-6 h-6" />
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Basic Information</h4>
              <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Tracking Number:</span>
                  <span className="text-sm font-medium">{shipment.trackingNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Provider ID:</span>
                  <span className="text-sm font-medium">{shipment.providerShipmentId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Order:</span>
                  <span className="text-sm font-medium">{shipment.order?.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Provider:</span>
                  <span className="text-sm font-medium">{shipment.shippingProvider?.displayName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Service:</span>
                  <span className="text-sm font-medium">{shipment.shipmentDetails?.serviceName}</span>
                </div>
              </div>
            </div>

            {/* Status Update */}
            {hasPermission('shipping', 'update') && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-2">Update Status</h4>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <select
                      value={newStatus || shipment.tracking?.status || ''}
                      onChange={(e) => setNewStatus(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    >
                      {statusOptions.map(status => (
                        <option key={status} value={status}>
                          {status.replace('_', ' ').toUpperCase()}
                        </option>
                      ))}
                    </select>
                    <button
                      onClick={handleStatusUpdate}
                      disabled={!newStatus || newStatus === shipment.tracking?.status}
                      className="px-4 py-2 bg-teal-600 text-white rounded-md hover:bg-teal-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                      Update
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Tracking Events */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Tracking Events</h4>
            <div className="bg-gray-50 p-3 rounded-lg max-h-96 overflow-y-auto">
              {shipment.tracking?.events?.length > 0 ? (
                <div className="space-y-3">
                  {shipment.tracking.events.map((event, index) => (
                    <div key={index} className="border-l-2 border-teal-200 pl-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-900 capitalize">
                          {event.status?.replace('_', ' ')}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(event.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{event.description}</p>
                      {event.location && (
                        <p className="text-xs text-gray-500">
                          {event.location.facility}, {event.location.city}, {event.location.state}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No tracking events available</p>
              )}
            </div>
          </div>
        </div>

        {/* Package Details */}
        {shipment.shipmentDetails?.packages?.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Package Details</h4>
            <div className="bg-gray-50 p-3 rounded-lg">
              {shipment.shipmentDetails.packages.map((pkg, index) => (
                <div key={index} className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Weight:</span>
                    <span className="ml-1 font-medium">{pkg.weight?.toFixed(2)} kg</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Dimensions:</span>
                    <span className="ml-1 font-medium">
                      {pkg.dimensions?.length}×{pkg.dimensions?.width}×{pkg.dimensions?.height} {pkg.dimensions?.unit}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Value:</span>
                    <span className="ml-1 font-medium">${pkg.value} {pkg.currency}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Contents:</span>
                    <span className="ml-1 font-medium">{pkg.contents}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ShippingManagement;
