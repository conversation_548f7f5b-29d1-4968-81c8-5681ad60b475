import axios from 'axios';

const api = axios.create({
    baseURL: 'http://localhost:5001', // Admin backend runs on port 5001
});

// Admin API instance with authentication
const adminAPI = axios.create({
    baseURL: 'http://localhost:5001/api/admin',
});

// Add auth token to admin requests
adminAPI.interceptors.request.use((config) => {
    const token = localStorage.getItem('adminToken');
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Handle auth errors
adminAPI.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('adminToken');
            window.location.href = '/admin/login';
        }
        return Promise.reject(error);
    }
);

export { adminAPI };
export default api;
