import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { 
  Search, 
  Star, 
  Eye, 
  Edit, 
  Trash2,
  MessageSquare,
  User,
  TrendingUp,
  RefreshCw,
  Shield
} from 'lucide-react';
import ResponsiveCard from '../../components/common/ResponsiveCard';
import ResponsiveGrid from '../../components/common/ResponsiveGrid';
import ResponsiveStatsCard from '../../components/common/ResponsiveStatsCard';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import Pagination from '../../components/common/Pagination';
import UserAvatar from '../../components/common/UserAvatar';
import {
  ResponsiveTable,
  ResponsiveTableHeader,
  ResponsiveTableBody,
  ResponsiveTableRow,
  ResponsiveTableCell,
  MobileCard,
  MobileCardHeader,
  MobileCardContent,
  MobileCardField,
  MobileCardActions
} from '../../components/common/ResponsiveTable';
import { adminApi } from '../../services/adminApi';

const RatingManagement = () => {
  const [ratings, setRatings] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    search: '',
    rating: '',
    ratingType: '',
    status: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    dateFrom: '',
    dateTo: ''
  });
  const [pagination, setPagination] = useState({});
  const [selectedRating, setSelectedRating] = useState(null);
  const [showModerationModal, setShowModerationModal] = useState(false);

  // Load ratings
  const loadRatings = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getAllRatings(filters);

      if (response && response.success) {
        setRatings(response.data.ratings || []);
        setPagination(response.data.pagination || {});
      } else {
        console.warn('Invalid ratings response format:', response);
        setRatings([]);
        setPagination({});
      }
    } catch (error) {
      console.error('Error loading ratings:', error);
      // Don't show error toast on initial load to prevent spam
      if (ratings.length === 0) {
        console.warn('Failed to load ratings on initial load');
      } else {
        toast.error('Failed to load ratings');
      }
      setRatings([]);
      setPagination({});
    } finally {
      setLoading(false);
    }
  };

  // Load rating statistics
  const loadStats = async () => {
    try {
      const response = await adminApi.getRatingStats();
      if (response && response.success) {
        setStats(response.data || {});
      } else {
        console.warn('Invalid rating stats response format:', response);
        setStats({});
      }
    } catch (error) {
      console.error('Error loading rating stats:', error);
      setStats({});
    }
  };

  useEffect(() => {
    loadRatings();
  }, [filters]);

  useEffect(() => {
    loadStats();
  }, []);

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  // Handle page change
  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Handle rating moderation
  const handleModerateRating = async (ratingId, status, moderationNotes = '') => {
    try {
      // Validate ratingId
      if (!ratingId || ratingId === 'undefined' || ratingId === 'unknown') {
        toast.error('Invalid rating ID. Please refresh the page and try again.');
        return;
      }

      const response = await adminApi.updateRatingStatus(ratingId, {
        status,
        moderationNotes
      });

      if (response.success) {
        toast.success('Rating status updated successfully');
        loadRatings();
        setShowModerationModal(false);
        setSelectedRating(null);
      }
    } catch (error) {
      console.error('Error updating rating status:', error);
      toast.error('Failed to update rating status');
    }
  };

  // Handle rating deletion
  const handleDeleteRating = async (ratingId) => {
    // Validate ratingId
    if (!ratingId || ratingId === 'undefined' || ratingId === 'unknown') {
      toast.error('Invalid rating ID. Please refresh the page and try again.');
      return;
    }

    if (!window.confirm('Are you sure you want to delete this rating?')) {
      return;
    }

    try {
      const response = await adminApi.deleteRating(ratingId);

      if (response.success) {
        toast.success('Rating deleted successfully');
        loadRatings();
      }
    } catch (error) {
      console.error('Error deleting rating:', error);
      toast.error('Failed to delete rating');
    }
  };

  // Format date
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    const colors = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'submitted': 'bg-blue-100 text-blue-800',
      'published': 'bg-green-100 text-green-800',
      'hidden': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  // Get rating status badge color (alias for consistency)
  const getRatingStatusBadgeColor = getStatusBadgeColor;

  // Render star rating
  const renderStarRating = (rating) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">({rating})</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Rating Management</h1>
          <p className="text-gray-600 mt-1">Manage and moderate user ratings</p>
        </div>
        <button
          onClick={() => {
            loadRatings();
            loadStats();
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh
        </button>
      </div>

      {/* Statistics */}
      <ResponsiveGrid cols={4} gap="medium">
        <ResponsiveStatsCard
          title="Total Ratings"
          value={stats.totalRatings || 0}
          icon={<Star className="w-full h-full text-yellow-600" />}
        />
        <ResponsiveStatsCard
          title="Average Rating"
          value={stats.averageRating ? stats.averageRating.toFixed(1) : '0.0'}
          icon={<TrendingUp className="w-full h-full text-green-600" />}
        />
        <ResponsiveStatsCard
          title="Recent Ratings"
          value={stats.recentRatings || 0}
          change="Last 30 days"
          icon={<MessageSquare className="w-full h-full text-blue-600" />}
        />
        <ResponsiveStatsCard
          title="Moderated"
          value={stats.moderatedRatings || 0}
          icon={<Shield className="w-full h-full text-purple-600" />}
        />
      </ResponsiveGrid>

      {/* Filters and Table */}
      <ResponsiveCard>
        {/* Filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search reviews..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Rating Filter */}
            <select
              value={filters.rating}
              onChange={(e) => handleFilterChange('rating', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Ratings</option>
              <option value="5">5 Stars</option>
              <option value="4">4 Stars</option>
              <option value="3">3 Stars</option>
              <option value="2">2 Stars</option>
              <option value="1">1 Star</option>
            </select>

            {/* Rating Type Filter */}
            <select
              value={filters.ratingType}
              onChange={(e) => handleFilterChange('ratingType', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Types</option>
              <option value="buyer_to_seller">Buyer to Seller</option>
              <option value="seller_to_buyer">Seller to Buyer</option>
            </select>

            {/* Status Filter */}
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="submitted">Submitted</option>
              <option value="published">Published</option>
              <option value="hidden">Hidden</option>
            </select>

            {/* Date From */}
            <input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Ratings Table */}
        <div className="overflow-x-auto">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <LoadingSpinner />
            </div>
          ) : ratings.length === 0 ? (
            <div className="text-center py-12">
              <Star className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No ratings found</h3>
              <p className="mt-1 text-sm text-gray-500">
                No ratings match your current filters.
              </p>
            </div>
          ) : (
            <ResponsiveTable>
              <ResponsiveTableHeader>
                <tr>
                  <ResponsiveTableCell header>Rating</ResponsiveTableCell>
                  <ResponsiveTableCell header>Reviewer</ResponsiveTableCell>
                  <ResponsiveTableCell header>Reviewed User</ResponsiveTableCell>
                  <ResponsiveTableCell header>Product</ResponsiveTableCell>
                  <ResponsiveTableCell header>Review</ResponsiveTableCell>
                  <ResponsiveTableCell header>Type</ResponsiveTableCell>
                  <ResponsiveTableCell header>Status</ResponsiveTableCell>
                  <ResponsiveTableCell header>Date</ResponsiveTableCell>
                  <ResponsiveTableCell header>Actions</ResponsiveTableCell>
                </tr>
              </ResponsiveTableHeader>
              <ResponsiveTableBody>
                {ratings.map((rating) => (
                  <ResponsiveTableRow
                    key={rating._id}
                    mobileCard={
                      <MobileCard>
                        <MobileCardHeader>
                          <div className="flex items-center space-x-2">
                            {renderStarRating(rating.rating)}
                            <span className="text-sm text-gray-500">
                              {rating.ratingType === 'buyer' ? 'Buyer Review' : 'Seller Review'}
                            </span>
                          </div>
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRatingStatusBadgeColor(rating.status || 'pending')}`}>
                            {rating.status || 'pending'}
                          </span>
                        </MobileCardHeader>

                        <MobileCardContent>
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">Reviewer:</span>
                            <UserAvatar user={rating.ratedBy} size="sm" showName />
                          </div>

                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm text-gray-500">Reviewed User:</span>
                            <UserAvatar user={rating.ratedUser} size="sm" showName />
                          </div>

                          <MobileCardField
                            label="Product"
                            value={rating.product?.title || 'N/A'}
                          />

                          {rating.review && (
                            <div className="mt-2">
                              <span className="text-sm text-gray-500">Review:</span>
                              <p className="text-sm text-gray-900 mt-1 line-clamp-2">
                                {rating.review}
                              </p>
                            </div>
                          )}

                          <MobileCardField
                            label="Date"
                            value={formatDate(rating.createdAt)}
                          />
                        </MobileCardContent>

                        <MobileCardActions>
                          <button
                            onClick={() => {
                              setSelectedRating(rating);
                              setShowModerationModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 p-1"
                            title="Moderate"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteRating(rating._id || 'unknown')}
                            className="text-red-600 hover:text-red-900 p-1"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </MobileCardActions>
                      </MobileCard>
                    }
                  >
                    <ResponsiveTableCell>
                      {renderStarRating(rating.rating)}
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <UserAvatar user={rating.ratedBy} showName />
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <UserAvatar user={rating.ratedUser} showName />
                    </ResponsiveTableCell>
                    <ResponsiveTableCell>
                      <div className="text-sm font-medium text-gray-900">
                        {rating.product?.title || 'N/A'}
                      </div>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="text-sm text-gray-900 max-w-xs truncate">
                        {rating.review || 'No review text'}
                      </div>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <span className="text-sm text-gray-900">
                        {rating.ratingType === 'buyer_to_seller' ? 'Buyer → Seller' : 'Seller → Buyer'}
                      </span>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRatingStatusBadgeColor(rating.status || 'pending')}`}>
                        {rating.status || 'pending'}
                      </span>
                    </ResponsiveTableCell>

                    <ResponsiveTableCell className="text-sm text-gray-500">
                      {formatDate(rating.createdAt)}
                    </ResponsiveTableCell>

                    <ResponsiveTableCell>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => {
                            setSelectedRating(rating);
                            setShowModerationModal(true);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                          title="Moderate Rating"
                        >
                          <Shield className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteRating(rating._id || 'unknown')}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Rating"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </ResponsiveTableCell>
                  </ResponsiveTableRow>
                ))}
              </ResponsiveTableBody>
            </ResponsiveTable>
          )}
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </ResponsiveCard>

      {/* Moderation Modal */}
      {showModerationModal && selectedRating && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Moderate Rating
              </h3>
              
              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">Rating: {renderStarRating(selectedRating.rating)}</p>
                <p className="text-sm text-gray-600 mb-2">Review: {selectedRating.review || 'No review text'}</p>
                <p className="text-sm text-gray-600">Current Status: {selectedRating.status || 'pending'}</p>
              </div>

              <div className="flex flex-col gap-2">
                <button
                  onClick={() => handleModerateRating(selectedRating._id || 'unknown', 'published')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  Publish
                </button>
                <button
                  onClick={() => handleModerateRating(selectedRating._id || 'unknown', 'hidden')}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                >
                  Hide
                </button>
                <button
                  onClick={() => {
                    setShowModerationModal(false);
                    setSelectedRating(null);
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RatingManagement;
