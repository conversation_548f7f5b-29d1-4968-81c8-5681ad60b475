import React, { useState, useEffect } from 'react';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';

const AdminDebug = () => {
  const { admin, isAuthenticated, hasPermission } = useAdminAuth();
  const [apiTests, setApiTests] = useState({});
  const [loading, setLoading] = useState(false);

  const testAPI = async (endpoint, method) => {
    try {
      setLoading(true);
      let response;
      
      switch (endpoint) {
        case 'orders':
          response = await adminApi.getAllOrders({ page: 1, limit: 5 });
          break;
        case 'orderStats':
          response = await adminApi.getOrderStats();
          break;
        case 'ratings':
          response = await adminApi.getAllRatings({ page: 1, limit: 5 });
          break;
        case 'ratingStats':
          response = await adminApi.getRatingStats();
          break;
        case 'reports':
          response = await adminApi.getAllReports({ page: 1, limit: 5 });
          break;
        case 'reportStats':
          response = await adminApi.getReportStats();
          break;
        default:
          throw new Error('Unknown endpoint');
      }
      
      setApiTests(prev => ({
        ...prev,
        [endpoint]: {
          success: true,
          data: response,
          error: null
        }
      }));
    } catch (error) {
      setApiTests(prev => ({
        ...prev,
        [endpoint]: {
          success: false,
          data: null,
          error: error.message
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const testAllAPIs = async () => {
    const endpoints = ['orders', 'orderStats', 'ratings', 'ratingStats', 'reports', 'reportStats'];
    for (const endpoint of endpoints) {
      await testAPI(endpoint);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">🔧 Admin Debug Panel</h2>
      
      {/* Authentication Status */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">Authentication Status</h3>
        <div className="bg-gray-50 p-4 rounded-lg">
          <p><strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
          <p><strong>Admin ID:</strong> {admin?.id || 'N/A'}</p>
          <p><strong>Admin Email:</strong> {admin?.email || 'N/A'}</p>
          <p><strong>Admin Role:</strong> {admin?.role || 'N/A'}</p>
        </div>
      </div>

      {/* Permissions */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">Permissions</h3>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <p><strong>Orders:</strong></p>
              <p>View: {hasPermission('orders', 'view') ? '✅' : '❌'}</p>
              <p>Update: {hasPermission('orders', 'update') ? '✅' : '❌'}</p>
            </div>
            <div>
              <p><strong>Ratings:</strong></p>
              <p>View: {hasPermission('ratings', 'view') ? '✅' : '❌'}</p>
              <p>Update: {hasPermission('ratings', 'update') ? '✅' : '❌'}</p>
            </div>
            <div>
              <p><strong>Reports:</strong></p>
              <p>View: {hasPermission('reports', 'view') ? '✅' : '❌'}</p>
              <p>Update: {hasPermission('reports', 'update') ? '✅' : '❌'}</p>
            </div>
          </div>
        </div>
      </div>

      {/* API Tests */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">API Tests</h3>
        <button
          onClick={testAllAPIs}
          disabled={loading}
          className="mb-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test All APIs'}
        </button>
        
        <div className="space-y-4">
          {Object.entries(apiTests).map(([endpoint, result]) => (
            <div key={endpoint} className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-800 mb-2">{endpoint}</h4>
              {result.success ? (
                <div className="text-green-600">
                  <p>✅ Success</p>
                  <p className="text-sm text-gray-600">
                    Data: {JSON.stringify(result.data).substring(0, 100)}...
                  </p>
                </div>
              ) : (
                <div className="text-red-600">
                  <p>❌ Failed</p>
                  <p className="text-sm">Error: {result.error}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Test */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">Navigation Test</h3>
        <div className="space-x-2">
          <a href="/admin/orders" className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            Go to Orders
          </a>
          <a href="/admin/ratings" className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700">
            Go to Ratings
          </a>
          <a href="/admin/reports" className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
            Go to Reports
          </a>
        </div>
      </div>

      {/* Local Storage Debug */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-2">Local Storage</h3>
        <div className="bg-gray-50 p-4 rounded-lg text-sm">
          <p><strong>Admin Access Token:</strong> {localStorage.getItem('adminAccessToken') ? '✅ Present' : '❌ Missing'}</p>
          <p><strong>Admin Refresh Token:</strong> {localStorage.getItem('adminRefreshToken') ? '✅ Present' : '❌ Missing'}</p>
        </div>
      </div>
    </div>
  );
};

export default AdminDebug;
