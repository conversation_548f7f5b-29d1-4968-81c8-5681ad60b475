/**
 * Utility functions for handling image URLs in the admin panel
 */

const BASE_URL = 'http://localhost:5001'; // Admin API port

/**
 * Get the full image URL by adding base URL if needed
 * @param {string} imagePath - The image path from the database
 * @returns {string} - The full image URL
 */
export const getImageUrl = (imagePath) => {
  if (!imagePath) return null;
  
  // If it's already a full URL, return as is
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }
  
  // Add base URL if it's a relative path
  return `${BASE_URL}${imagePath.startsWith('/') ? imagePath : `/${imagePath}`}`;
};

/**
 * Get the first product image URL
 * @param {Array} productPhotos - Array of product photo paths
 * @returns {string|null} - The first image URL or null
 */
export const getProductImageUrl = (productPhotos) => {
  if (!productPhotos || !Array.isArray(productPhotos) || productPhotos.length === 0) {
    return null;
  }
  
  return getImageUrl(productPhotos[0]);
};

/**
 * Get user profile image URL with fallback to initials
 * @param {Object} user - User object with profile, firstName, lastName
 * @returns {Object} - Object with imageUrl and initials
 */
export const getUserImageData = (user) => {
  const imageUrl = user?.profile ? getImageUrl(user.profile) : null;
  const initials = `${user?.firstName?.[0]?.toUpperCase() || ''}${user?.lastName?.[0]?.toUpperCase() || ''}`;
  
  return {
    imageUrl,
    initials,
    hasImage: !!imageUrl
  };
};
