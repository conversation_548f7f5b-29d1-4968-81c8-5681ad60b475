import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import usePageTitle from '../../hooks/usePageTitle';
import { adminApi } from '../../services/adminApi';
import {
  Container,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveButton,
  ResponsiveModal,
  ResponsiveFormGrid,
  ResponsiveInput
} from '../../components/common/ResponsiveLayout';
import { combineClasses } from '../../utils/responsive';

const Categories = () => {
  usePageTitle('Admin | Categories');
  const [searchTerm, setSearchTerm] = useState('');
  const [categories, setCategories] = useState([]); // State to hold categories from backend
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false); // State to control drawer visibility
  const [newCategoryData, setNewCategoryData] = useState({
    category: '',
    subcategories: [],
    hasSubcategories: false, // Checkbox state
  });
  const [editingCategory, setEditingCategory] = useState(null); // State to hold category being edited
  const [newSubcategoryInput, setNewSubcategoryInput] = useState(''); // State for the subcategory input field

  // Fetch categories from backend on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await adminApi.getCategories();
      console.log('🔍 Raw API response:', response);

      // The backend returns an array of objects like { _id: '...', name: '...', subCategories: [...] }
      const formattedCategories = response.categories.map((item) => ({
        id: item._id, // Use the actual backend _id
        name: item.name,
        subcategories: (item.subCategories || []).map(sub => sub.name), // Extract subcategory names
        isSubCategories: (item.subCategories && item.subCategories.length > 0), // Calculate based on subCategories
        // Assuming productCount and status are not managed here based on the backend structure provided
        productCount: 0, // Placeholder
        status: 'active', // Placeholder
      }));
      console.log('✅ Formatted categories:', formattedCategories);
      setCategories(formattedCategories);
    } catch (error) {
      console.error('❌ Error fetching categories:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategoryClick = () => {
    setEditingCategory(null);
    setNewCategoryData({ category: '', subcategories: [], hasSubcategories: false });
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
    setNewCategoryData({ category: '', subcategories: [], hasSubcategories: false });
    setEditingCategory(null);
    setNewSubcategoryInput('');
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewCategoryData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddSubcategoryInput = (e) => {
      setNewSubcategoryInput(e.target.value);
  };

    const handleAddSubcategory = () => {
        if (newSubcategoryInput.trim() !== '') {
            // Frontend validation for duplicate subcategory names within the same category
            if (newCategoryData.subcategories.includes(newSubcategoryInput.trim())) {
                 alert('Subcategory already exists in this category.');
                 return;
            }
            setNewCategoryData(prev => ({
                ...prev,
                subcategories: [...prev.subcategories, newSubcategoryInput.trim()]
            }));
            setNewSubcategoryInput('');
        }
    };

    const handleRemoveSubcategory = (subToRemove) => {
        setNewCategoryData(prev => ({
            ...prev,
            subcategories: prev.subcategories.filter(sub => sub !== subToRemove)
        }));
    };

  const handleSaveCategory = async (e) => {
    e.preventDefault();

    // Frontend validation for duplicate category name when adding
    if (!editingCategory && categories.find(cat => cat.name.trim() === newCategoryData.category.trim())) {
        alert('Category with this name already exists.');
        return;
    }

    // Validation: If 'Has Subcategories' is checked, require at least one subcategory
    if (newCategoryData.hasSubcategories && newCategoryData.subcategories.length === 0) {
        alert('Please add at least one subcategory.');
        return;
    }

    const categoryPayload = {
        category: newCategoryData.category.trim(),
        subcategories: newCategoryData.hasSubcategories ? newCategoryData.subcategories : [],
        isSubCategories: newCategoryData.hasSubcategories // Include the isSubCategories flag
    };

    try {
      if (editingCategory) {
        // Handle Edit (PUT request)
        await adminApi.updateCategory(editingCategory.id, categoryPayload);
      } else {
        // Handle Add (POST request)
        await adminApi.createCategory(categoryPayload);
      }
      fetchCategories(); // Refresh the list
      handleDrawerClose(); // Close the drawer
    } catch (error) {
      console.error('Error saving category:', error);
      // Handle error (e.g., show an alert)
      // You might want to check error.response.status for specific backend errors (e.g., 409 for duplicate)
       if (error.response && error.response.status === 409) {
            alert(error.response.data.msg);
       } else {
            alert(error.response.data.msg);
            // alert('Failed to save category.');
       }
    }
  };

    const handleEditCategory = (category) => {
        setEditingCategory(category);
        setNewCategoryData({
            category: category.name,
            subcategories: category.subcategories || [],
            hasSubcategories: category.isSubCategories,
        });
        setIsDrawerOpen(true);
    };

    const handleDeleteCategory = async (categoryId) => {
        if (window.confirm(`Are you sure you want to delete this category?`)) {
            try {
                await adminApi.deleteCategory(categoryId);
                fetchCategories(); // Refresh the list
            } catch (error) {
                console.error('Error deleting category:', error);
                 alert('Failed to delete category.');
            }
        }
    };


  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  console.log('📊 Categories state:', categories);
  console.log('🔍 Filtered categories:', filteredCategories);

  if (loading) {
    return (
      <Container>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg text-gray-600">Loading categories...</div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <ResponsiveCard className="bg-red-50 border-red-200">
          <div className="text-red-800">Error loading categories: {error}</div>
          <ResponsiveButton
            onClick={fetchCategories}
            variant="danger"
            size="small"
            className="mt-4"
          >
            Retry
          </ResponsiveButton>
        </ResponsiveCard>
      </Container>
    );
  }

  return (
    <Container className="py-4 sm:py-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold">
          Categories Management ({categories.length})
        </h1>
        <ResponsiveButton
          onClick={handleAddCategoryClick}
          size="medium"
          className="w-full sm:w-auto"
        >
          Add New Category
        </ResponsiveButton>
      </div>

      {/* Search */}
      <ResponsiveCard className="mb-6 sm:mb-8">
        <input
          type="text"
          placeholder="Search categories..."
          className="w-full px-3 py-2 sm:px-4 sm:py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 text-sm sm:text-base"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </ResponsiveCard>

      {/* Categories Table */}
      <ResponsiveCard className="overflow-hidden p-0">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category Name
                </th>
                <th className="hidden sm:table-cell px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subcategories
                </th>
                <th className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredCategories.map((category) => (
              <tr key={category.id} className="hover:bg-gray-50">
                <td className="px-3 py-4 sm:px-6">
                  <div className="text-sm font-medium text-gray-900">{category.name}</div>
                  {/* Show subcategories on mobile */}
                  <div className="sm:hidden mt-2">
                    <div className="flex flex-wrap gap-1">
                      {category.subcategories.slice(0, 3).map((sub, index) => (
                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                          {sub}
                        </span>
                      ))}
                      {category.subcategories.length > 3 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">
                          +{category.subcategories.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                <td className="hidden sm:table-cell px-3 py-4 sm:px-6">
                  <div className="flex flex-wrap gap-1">
                    {category.subcategories.map((sub, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                        {sub}
                      </span>
                    ))}
                  </div>
                </td>
                <td className="px-3 py-4 sm:px-6 text-sm font-medium">
                  <div className="flex flex-col sm:flex-row gap-2">
                    <ResponsiveButton
                      onClick={() => handleEditCategory(category)}
                      variant="outline"
                      size="small"
                      className="text-teal-600 border-teal-600 hover:bg-teal-50"
                    >
                      Edit
                    </ResponsiveButton>
                    <ResponsiveButton
                      onClick={() => handleDeleteCategory(category.id)}
                      variant="danger"
                      size="small"
                    >
                      Delete
                    </ResponsiveButton>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        </div>
        {filteredCategories.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No categories found.
          </div>
        )}
      </ResponsiveCard>

      {/* Custom Drawer */}
      {isDrawerOpen && (
        <div className="fixed inset-0 overflow-hidden z-50">
          <div className="absolute inset-0 overflow-hidden">
            {/* Background overlay */}
            <div className="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={handleDrawerClose}></div>

            {/* Drawer panel */}
            <section className="absolute inset-y-0 right-0 pl-10 max-w-full flex">
              <div className="w-screen max-w-md">
                <div className="h-full flex flex-col py-6 bg-white shadow-xl overflow-y-auto">
                  <div className="px-4 sm:px-6">
                    <h2 className="text-lg font-medium text-gray-900">
                      {editingCategory ? 'Edit Category' : 'Add New Category'}
                    </h2>
                  </div>
                  <div className="relative mt-6 flex-1 px-4 sm:px-6">
                    {/* Form goes here */}
                    <form onSubmit={handleSaveCategory}>
                      <div className="mb-6">
                        <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">Category Name</label>
                        <input
                          type="text"
                          name="category"
                          id="category"
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-teal-500 focus:border-teal-500 sm:text-sm"
                          value={newCategoryData.category}
                          onChange={handleInputChange}
                          required
                        />
                         <div className="flex items-center mt-3">
                            <input
                                type="checkbox"
                                name="hasSubcategories"
                                id="hasSubcategories"
                                checked={newCategoryData.hasSubcategories}
                                onChange={handleInputChange}
                                className="focus:ring-teal-500 h-4 w-4 text-teal-600 border-gray-300 rounded"
                            />
                            <label htmlFor="hasSubcategories" className="ml-2 block text-sm text-gray-900 cursor-pointer">
                                Has Subcategories?
                            </label>
                        </div>
                      </div>

                       {newCategoryData.hasSubcategories && (
                           <div className="mb-6">
                                <label htmlFor="subcategoryInput" className="block text-sm font-medium text-gray-700 mb-1">Subcategories</label>
                                <div className="flex mt-1">
                                    <input
                                        type="text"
                                        id="subcategoryInput"
                                        className="block w-full px-3 py-2 shadow-sm sm:text-sm border border-gray-300 rounded-l-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                                        value={newSubcategoryInput}
                                        onChange={handleAddSubcategoryInput}
                                        onKeyPress={(e) => { if (e.key === 'Enter') { e.preventDefault(); handleAddSubcategory(); } }}
                                        placeholder="Add subcategory and press Enter"
                                    />
                                     <button
                                        type="button"
                                        onClick={handleAddSubcategory}
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                                     >
                                        Add
                                     </button>
                                </div>
                                 <div className="mt-3 flex flex-wrap gap-2">
                                    {newCategoryData.subcategories.map((sub, index) => (
                                        <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {sub}
                                            <button
                                                type="button"
                                                onClick={() => handleRemoveSubcategory(sub)}
                                                className="flex-shrink-0 ml-1 h-4 w-4 rounded-full inline-flex items-center justify-center text-blue-400 hover:bg-blue-200 hover:text-blue-500 focus:outline-none focus:bg-blue-500 focus:text-white"
                                            >
                                                 <span className="sr-only">Remove subcategory</span>
                                                <svg className="h-2 w-2" stroke="currentColor" fill="none" viewBox="0 0 8 8">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M1 1l6 6m0-6L1 7" />
                                                </svg>
                                            </button>
                                        </span>
                                    ))}
                                </div>
                           </div>
                       )}

                      <div className="flex justify-end py-4">
                        <button
                          type="button"
                          className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                          onClick={handleDrawerClose}
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="ml-4 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                          {editingCategory ? 'Update' : 'Save'}
                        </button>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      )}
    </Container>
  );
};

export default Categories; 