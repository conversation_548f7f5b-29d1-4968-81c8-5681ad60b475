import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';
import usePageTitle from '../../hooks/usePageTitle';
import {
  Container,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveStatsCard,
  ResponsiveButton
} from '../../components/common/ResponsiveLayout';
import { combineClasses } from '../../utils/responsive';

const Dashboard = () => {
  usePageTitle('Admin | Dashboard');

  const { admin, hasPermission } = useAdminAuth();
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getDashboardStats();
      if (response.success) {
        setDashboardStats(response.data.stats);
      } else {
        setError('Failed to load dashboard statistics');
      }
    } catch (error) {
      console.error('Dashboard stats error:', error);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <ResponsiveCard className="bg-red-50 border-red-200 text-red-600">
          {error}
        </ResponsiveCard>
      </Container>
    );
  }

  return (
    <Container className="py-4 sm:py-6">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600 text-sm sm:text-base mt-1">
          Welcome back, {admin?.firstName} {admin?.lastName}
        </p>
      </div>

      {/* Stats Overview */}
      <ResponsiveGrid cols={4} gap="medium" className="mb-6 sm:mb-8">
        <ResponsiveStatsCard
          title="Total Users"
          value={dashboardStats?.users?.total || 0}
          change={`+${dashboardStats?.users?.growth?.toFixed(1) || 0}% from last month`}
          icon={
            <svg className="w-full h-full text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          }
        />

        <ResponsiveStatsCard
          title="Total Products"
          value={dashboardStats?.products?.total || 0}
          change={`+${dashboardStats?.products?.growth?.toFixed(1) || 0}% from last month`}
          icon={
            <svg className="w-full h-full text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          }
        />

        <ResponsiveStatsCard
          title="Total Orders"
          value={dashboardStats?.orders?.total || 0}
          change={`+${dashboardStats?.orders?.growth?.toFixed(1) || 0}% from last month`}
          icon={
            <svg className="w-full h-full text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
            </svg>
          }
        />

        <ResponsiveStatsCard
          title="Total Revenue"
          value={`$${dashboardStats?.revenue?.total?.toLocaleString() || 0}`}
          change={`+${dashboardStats?.revenue?.growth?.toFixed(1) || 0}% from last month`}
          icon={
            <svg className="w-full h-full text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
        />
      </ResponsiveGrid>

      {/* Quick Actions */}
      <ResponsiveCard className="mb-6 sm:mb-8">
        <h2 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Quick Actions</h2>
        <ResponsiveGrid cols={4} gap="medium">
          {hasPermission('users', 'view') && (
            <Link
              to="/admin/users"
              className="bg-blue-500 text-white p-3 sm:p-4 rounded-lg text-center hover:bg-blue-600 transition-colors duration-200 text-sm sm:text-base"
            >
              <div className="flex flex-col items-center">
                <svg className="w-6 h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                Manage Users
              </div>
            </Link>
          )}

          {hasPermission('listings', 'view') && (
            <Link
              to="/admin/listings"
              className="bg-green-500 text-white p-3 sm:p-4 rounded-lg text-center hover:bg-green-600 transition-colors duration-200 text-sm sm:text-base"
            >
              <div className="flex flex-col items-center">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
                <span className="hidden sm:inline">Manage Listings</span>
                <span className="sm:hidden">Listings</span>
              </div>
            </Link>
          )}

          {/* {hasPermission('disputes', 'view') && (
            <Link
              to="/admin/disputes"
              className="bg-yellow-500 text-white p-3 sm:p-4 rounded-lg text-center hover:bg-yellow-600 transition-colors duration-200 text-sm sm:text-base"
            >
              <div className="flex flex-col items-center">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="hidden sm:inline">View Disputes</span>
                <span className="sm:hidden">Disputes</span>
              </div>
            </Link>
          )} */}

          {/* {hasPermission('counterfeit', 'view') && (
            <Link
              to="/admin/counterfeit"
              className="bg-red-500 text-white p-3 sm:p-4 rounded-lg text-center hover:bg-red-600 transition-colors duration-200 text-sm sm:text-base"
            >
              <div className="flex flex-col items-center">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="hidden sm:inline">Counterfeit Reports</span>
                <span className="sm:hidden">Reports</span>
              </div>
            </Link>
          )} */}

          {hasPermission('analytics', 'view') && (
            <Link
              to="/admin/analytics"
              className="bg-purple-500 text-white p-3 sm:p-4 rounded-lg text-center hover:bg-purple-600 transition-colors duration-200 text-sm sm:text-base"
            >
              <div className="flex flex-col items-center">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span className="hidden sm:inline">View Analytics</span>
                <span className="sm:hidden">Analytics</span>
              </div>
            </Link>
          )}

          <Link
            to="/admin/categories"
            className="bg-teal-500 text-white p-3 sm:p-4 rounded-lg text-center hover:bg-teal-600 transition-colors duration-200 text-sm sm:text-base"
          >
            <div className="flex flex-col items-center">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
              <span className="hidden sm:inline">Manage Categories</span>
              <span className="sm:hidden">Categories</span>
            </div>
          </Link>
        </ResponsiveGrid>
      </ResponsiveCard>

      {/* System Status & Recent Activity */}
      <ResponsiveGrid cols={2} gap="medium">
        {/* System Status */}
        <ResponsiveCard>
          <h2 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">System Status</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                <span className="text-sm font-medium">Active Users</span>
              </div>
              <span className="text-sm text-gray-600">{dashboardStats?.users?.active || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                <span className="text-sm font-medium">Active Products</span>
              </div>
              <span className="text-sm text-gray-600">{dashboardStats?.products?.active || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-400 rounded-full mr-3"></div>
                <span className="text-sm font-medium">Completed Orders</span>
              </div>
              <span className="text-sm text-gray-600">{dashboardStats?.orders?.completed || 0}</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                <span className="text-sm font-medium">Monthly Revenue</span>
              </div>
              <span className="text-sm text-gray-600">${dashboardStats?.revenue?.monthly?.toLocaleString() || 0}</span>
            </div>
          </div>
        </ResponsiveCard>

        {/* Quick Stats */}
        <ResponsiveCard>
          <h2 className="text-lg sm:text-xl font-semibold mb-4 sm:mb-6">Quick Stats</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-600">User Growth</p>
                <p className="text-lg font-bold text-gray-900">
                  {dashboardStats?.users?.growth > 0 ? '+' : ''}{dashboardStats?.users?.growth?.toFixed(1) || 0}%
                </p>
              </div>
              <div className={`text-2xl ${dashboardStats?.users?.growth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {dashboardStats?.users?.growth >= 0 ? '↗' : '↘'}
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-600">Product Growth</p>
                <p className="text-lg font-bold text-gray-900">
                  {dashboardStats?.products?.growth > 0 ? '+' : ''}{dashboardStats?.products?.growth?.toFixed(1) || 0}%
                </p>
              </div>
              <div className={`text-2xl ${dashboardStats?.products?.growth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {dashboardStats?.products?.growth >= 0 ? '↗' : '↘'}
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="text-sm font-medium text-gray-600">Revenue Growth</p>
                <p className="text-lg font-bold text-gray-900">
                  {dashboardStats?.revenue?.growth > 0 ? '+' : ''}{dashboardStats?.revenue?.growth?.toFixed(1) || 0}%
                </p>
              </div>
              <div className={`text-2xl ${dashboardStats?.revenue?.growth >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                {dashboardStats?.revenue?.growth >= 0 ? '↗' : '↘'}
              </div>
            </div>
          </div>
        </ResponsiveCard>
      </ResponsiveGrid>
    </Container>
  );
};

export default Dashboard; 