import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { useAdminAuth } from '../../context/AdminAuthContext';
import { adminApi } from '../../services/adminApi';
import usePageTitle from '../../hooks/usePageTitle';
import {
  Container,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveTable,
  ResponsiveButton,
  ResponsiveFlex
} from '../../components/common/ResponsiveLayout';
import { combineClasses, TABLE_RESPONSIVE } from '../../utils/responsive';
import { getProductImageUrl } from '../../utils/imageUtils';

const ListingManagement = () => {
  usePageTitle('Admin | Listing Management');
  
  const { hasPermission } = useAdminAuth();
  const [listings, setListings] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    search: '',
    status: 'all',
    category: '',
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  const [pagination, setPagination] = useState({});
  const [selectedListings, setSelectedListings] = useState([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [actionReason, setActionReason] = useState('');
  const [actionNotes, setActionNotes] = useState('');
  const [searchInput, setSearchInput] = useState('');

  useEffect(() => {
    fetchListings();
  }, [filters]);

  useEffect(() => {
    fetchCategories();
  }, []);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters(prev => ({
        ...prev,
        search: searchInput,
        page: 1
      }));
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchInput]);

  const fetchListings = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getListings(filters);
      if (response.success) {
        setListings(response.data.listings);
        setPagination(response.data.pagination);
      } else {
        setError('Failed to load listings');
      }
    } catch (error) {
      console.error('Fetch listings error:', error);
      setError('Failed to load listings');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await adminApi.getCategories();
      if (response.success) {
        setCategories(response.data.categories || []);
      }
    } catch (error) {
      console.error('Fetch categories error:', error);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };



  const handleSort = (sortBy) => {
    const sortOrder = filters.sortBy === sortBy && filters.sortOrder === 'desc' ? 'asc' : 'desc';
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder
    }));
  };

  const handleSelectListing = (listingId) => {
    setSelectedListings(prev => 
      prev.includes(listingId) 
        ? prev.filter(id => id !== listingId)
        : [...prev, listingId]
    );
  };

  const handleSelectAll = () => {
    if (selectedListings.length === listings.length) {
      setSelectedListings([]);
    } else {
      setSelectedListings(listings.map(listing => listing._id));
    }
  };

  const handleListingAction = async (action, listingId, reason = '', notes = '') => {
    try {
      let response;
      switch (action) {
        case 'approve':
          response = await adminApi.approveListing(listingId, notes);
          break;
        case 'reject':
          response = await adminApi.rejectListing(listingId, reason, notes);
          break;
        case 'suspend':
          response = await adminApi.suspendListing(listingId, reason, notes);
          break;
        case 'delete':
          response = await adminApi.deleteListing(listingId);
          break;
        default:
          return;
      }

      if (response.success) {
        fetchListings(); // Refresh the list
        setShowActionModal(false);
        setActionReason('');
        setActionNotes('');
        setSelectedListings([]);
      } else {
        setError(response.message || 'Action failed');
      }
    } catch (error) {
      console.error('Listing action error:', error);
      setError('Action failed');
    }
  };

  const handleBulkAction = async () => {
    try {
      const response = await adminApi.bulkListingActions(
        actionType,
        selectedListings,
        actionReason,
        actionNotes
      );

      if (response.success) {
        fetchListings();
        setShowActionModal(false);
        setActionReason('');
        setActionNotes('');
        setSelectedListings([]);
      } else {
        setError(response.message || 'Bulk action failed');
      }
    } catch (error) {
      console.error('Bulk action error:', error);
      setError('Bulk action failed');
    }
  };

  const openActionModal = (action) => {
    setActionType(action);
    setShowActionModal(true);
  };

  const confirmAction = () => {
    if (selectedListings.length === 1) {
      handleListingAction(actionType, selectedListings[0], actionReason, actionNotes);
    } else {
      handleBulkAction();
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { bg: 'bg-green-100', text: 'text-green-800', label: 'Active' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Pending' },
      rejected: { bg: 'bg-red-100', text: 'text-red-800', label: 'Rejected' },
      suspended: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Suspended' },
      removed: { bg: 'bg-red-100', text: 'text-red-800', label: 'Removed' }
    };

    const config = statusConfig[status] || statusConfig.pending;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  if (loading && listings.length === 0) {
    return (
      <Container>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-4 sm:py-6">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Listing Management</h1>
        <p className="text-gray-600 text-sm sm:text-base mt-1">Manage and moderate product listings</p>
      </div>

      {error && (
        <ResponsiveCard className="mb-4 sm:mb-6 bg-red-50 border-red-200 text-red-600">
          {error}
        </ResponsiveCard>
      )}

      {/* Filters */}
      <ResponsiveCard className="mb-6 sm:mb-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              placeholder="Search listings..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category._id} value={category._id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
            <select
              value={filters.sortBy}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="createdAt">Date Created</option>
              <option value="title">Title</option>
              <option value="price">Price</option>
              <option value="status">Status</option>
            </select>
          </div>
        </div>
      </ResponsiveCard>

      {/* Bulk Actions */}
      {selectedListings.length > 0 && (
        <ResponsiveCard padding="small" className="mb-6 sm:mb-8">
          <ResponsiveFlex justify="between" align="center" className="flex-wrap gap-2">
            <span className="text-sm text-gray-600">
              {selectedListings.length} listing(s) selected
            </span>
            <div className="flex flex-wrap gap-2">
              {hasPermission('listings', 'approve') && (
                <button
                  onClick={() => openActionModal('approve')}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                >
                  Approve
                </button>
              )}
              {hasPermission('listings', 'reject') && (
                <button
                  onClick={() => openActionModal('reject')}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                >
                  Reject
                </button>
              )}
              {hasPermission('listings', 'edit') && (
                <button
                  onClick={() => openActionModal('suspend')}
                  className="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
                >
                  Suspend
                </button>
              )}
              {hasPermission('listings', 'delete') && (
                <button
                  onClick={() => openActionModal('delete')}
                  className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700"
                >
                  Delete
                </button>
              )}
            </div>
          </ResponsiveFlex>
        </ResponsiveCard>
      )}

      {/* Listings Table */}
      <ResponsiveTable>
            <thead className="bg-gray-50">
              <tr>
                <th className="px-3 py-3 sm:px-6 text-left">
                  <input
                    type="checkbox"
                    checked={selectedListings.length === listings.length && listings.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                  />
                </th>
                <th
                  className={combineClasses(
                    'px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100'
                  )}
                  onClick={() => handleSort('title')}
                >
                  Product
                  {filters.sortBy === 'title' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th className={combineClasses('px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider', TABLE_RESPONSIVE.hideOnMobile)}>
                  Seller
                </th>
                <th
                  className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('price')}
                >
                  Price
                  {filters.sortBy === 'price' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th
                  className="px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('status')}
                >
                  Status
                  {filters.sortBy === 'status' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th className={combineClasses('px-3 py-3 sm:px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider', TABLE_RESPONSIVE.hideOnTablet)}>
                  Category
                </th>
                <th 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort('createdAt')}
                >
                  Created
                  {filters.sortBy === 'createdAt' && (
                    <span className="ml-1">{filters.sortOrder === 'desc' ? '↓' : '↑'}</span>
                  )}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {listings.map((listing) => (
                <tr key={listing._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedListings.includes(listing._id)}
                      onChange={() => handleSelectListing(listing._id)}
                      className="rounded border-gray-300 text-teal-600 focus:ring-teal-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-12 w-12">
                        {(() => {
                          const imageUrl = getProductImageUrl(listing.product_photos);
                          return (
                            <>
                              {imageUrl && (
                                <img
                                  className="h-12 w-12 rounded-lg object-cover"
                                  src={imageUrl}
                                  alt={listing.title || 'Product image'}
                                  onError={(e) => {
                                    e.target.style.display = 'none';
                                    e.target.nextSibling.style.display = 'flex';
                                  }}
                                />
                              )}
                              <div
                                className={`h-12 w-12 rounded-lg bg-gray-300 flex items-center justify-center ${imageUrl ? 'hidden' : ''}`}
                              >
                                <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                              </div>
                            </>
                          );
                        })()}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                          {listing.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {listing.brand && `${listing.brand} • `}
                          {listing.condition}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {listing.user?.firstName} {listing.user?.lastName}
                    </div>
                    <div className="text-sm text-gray-500">@{listing.user?.userName}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${listing.price?.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(listing.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {listing.category?.name || 'Uncategorized'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(listing.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <Link
                        to={`/admin/listings/${listing._id}`}
                        className="text-teal-600 hover:text-teal-900"
                      >
                        View
                      </Link>
                      {hasPermission('listings', 'approve') && listing.status === 'pending' && (
                        <button
                          onClick={() => {
                            setSelectedListings([listing._id]);
                            openActionModal('approve');
                          }}
                          className="text-green-600 hover:text-green-900"
                        >
                          Approve
                        </button>
                      )}
                      {hasPermission('listings', 'reject') && listing.status === 'pending' && (
                        <button
                          onClick={() => {
                            setSelectedListings([listing._id]);
                            openActionModal('reject');
                          }}
                          className="text-red-600 hover:text-red-900"
                        >
                          Reject
                        </button>
                      )}
                      {hasPermission('listings', 'edit') && listing.status === 'active' && (
                        <button
                          onClick={() => {
                            setSelectedListings([listing._id]);
                            openActionModal('suspend');
                          }}
                          className="text-yellow-600 hover:text-yellow-900"
                        >
                          Suspend
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
      </ResponsiveTable>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handleFilterChange('page', filters.page - 1)}
                disabled={!pagination.hasPrev}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => handleFilterChange('page', filters.page + 1)}
                disabled={!pagination.hasNext}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing page <span className="font-medium">{pagination.currentPage}</span> of{' '}
                  <span className="font-medium">{pagination.totalPages}</span> ({pagination.totalListings} total listings)
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  <button
                    onClick={() => handleFilterChange('page', filters.page - 1)}
                    disabled={!pagination.hasPrev}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handleFilterChange('page', filters.page + 1)}
                    disabled={!pagination.hasNext}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}

      {/* Action Modal */}
      {showActionModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Confirm {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
              </h3>
              <p className="text-sm text-gray-500 mb-4">
                Are you sure you want to {actionType} {selectedListings.length} listing(s)?
              </p>
              
              {(actionType === 'reject' || actionType === 'suspend') && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reason (required)
                  </label>
                  <input
                    type="text"
                    value={actionReason}
                    onChange={(e) => setActionReason(e.target.value)}
                    placeholder={`Enter reason for ${actionType}...`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                  />
                </div>
              )}
              
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optional)
                </label>
                <textarea
                  value={actionNotes}
                  onChange={(e) => setActionNotes(e.target.value)}
                  placeholder="Additional notes..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-teal-500 focus:border-teal-500"
                  rows="3"
                />
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowActionModal(false);
                    setActionReason('');
                    setActionNotes('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmAction}
                  disabled={(actionType === 'reject' || actionType === 'suspend') && !actionReason}
                  className={`px-4 py-2 rounded-md text-white disabled:opacity-50 ${
                    actionType === 'delete' 
                      ? 'bg-gray-600 hover:bg-gray-700' 
                      : actionType === 'reject'
                      ? 'bg-red-600 hover:bg-red-700'
                      : actionType === 'suspend'
                      ? 'bg-yellow-600 hover:bg-yellow-700'
                      : 'bg-green-600 hover:bg-green-700'
                  }`}
                >
                  {actionType.charAt(0).toUpperCase() + actionType.slice(1)}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};

export default ListingManagement;
